from app import db
from sqlalchemy import BigInteger, String, Integer, DateTime, Boolean, Numeric, Date

class Item(db.Model):
    __bind_key__ = 'YszInventory'
    __tablename__ = 'Items'
    __table_args__ = {
        'schema': 'dbo',
        'extend_existing': True
    }

    id = db.Column('Id', BigInteger, primary_key=True)
    item_type = db.Column('ItemType', Integer, nullable=False)
    item_code = db.Column('ItemCode', String(13), nullable=False, default='')
    item_name = db.Column('ItemName', String(50), nullable=False, default='')
    section_id = db.Column('SectionId', BigInteger, 
                          db.ForeignKey('Sections.Id'), 
                          nullable=False, default=0)
    dept_id = db.Column('DeptId', BigInteger, 
                       db.<PERSON><PERSON>('Sections.Id'), 
                       nullable=False, default=0)
    supplier_code = db.Column('SupplierCode', String(10), nullable=False, default='')
    unit_cost = db.Column('UnitCost', Numeric(10, 3), nullable=False, default=0)
    unit_price = db.Column('UnitPrice', Integer, nullable=False, default=0)
    tax_type = db.Column('TaxType', Integer, nullable=False, default=1)
    tax_rate = db.Column('TaxRate', Numeric(3, 2), nullable=False, default=0.00)
    del_flg = db.Column('DelFlg', BigInteger, nullable=False, default=0)
    cr_date_time = db.Column('CrDateTime', DateTime, nullable=False, 
                            server_default=db.text('getdate()'))
    up_date_time = db.Column('UpDateTime', DateTime, nullable=False, 
                            server_default=db.text('getdate()'))
    receiving_unit = db.Column('ReceivingUnit', Numeric(10, 2), nullable=False, default=1)
    core_system_id = db.Column('CoreSystemId', BigInteger, nullable=True)
    temp_item_flg = db.Column('TempItemFlg', Boolean, nullable=False, default=False)
    temp_item_last_update = db.Column('TempItemLastUpdate', Date, nullable=True)
    temp_item_last_store_code = db.Column('TempItemLastStoreCode', String(6), nullable=True)

    # リレーションシップの定義
    section = db.relationship('Section', foreign_keys=[section_id], primaryjoin="Item.section_id == Section.id")
    department = db.relationship('Section', foreign_keys=[dept_id], primaryjoin="Item.dept_id == Section.id")

    def __repr__(self):
        return f"<Item {self.item_name}>"
