#!/usr/bin/env python3

# 最新のファイルを確認
filename = 'output/Shipment_20250526174858_001837.DAT'

with open(filename, 'r', encoding='shift_jis') as f:
    lines = f.readlines()

print(f'ファイル: {filename}')
print(f'総行数: {len(lines)}')

# NNN4レコード（明細）を探す
for i, line in enumerate(lines):
    line_content = line.rstrip('\n\r')
    
    # 256バイト行の前半128バイトまたは後半128バイトでNNN4を探す
    first_half = line_content[:128] if len(line_content) >= 128 else line_content
    second_half = line_content[128:256] if len(line_content) >= 256 else ""
    
    if first_half.startswith('NNN4'):
        print(f'\n行{i+1} 前半: NNN4レコード')
        print(f'  発注単位(54-57): {first_half[52:56]}')
        print(f'  単位区分(58-59): "{first_half[56:58]}"')
        print(f'  発注数量(60-65): {first_half[58:64]}')
        print(f'  入荷予定数(66-71): {first_half[64:70]}')
        print(f'  原単価(72-79): {first_half[70:78]}')
        print(f'  原価金額(80-87): {first_half[78:86]}')
        print(f'  売価(88-93): {first_half[86:92]}')
        print(f'  売価金額(94-101): {first_half[92:100]}')
    
    if second_half.startswith('NNN4'):
        print(f'\n行{i+1} 後半: NNN4レコード')
        print(f'  発注単位(54-57): {second_half[52:56]}')
        print(f'  単位区分(58-59): "{second_half[56:58]}"')
        print(f'  発注数量(60-65): {second_half[58:64]}')
        print(f'  入荷予定数(66-71): {second_half[64:70]}')
        print(f'  原単価(72-79): {second_half[70:78]}')
        print(f'  原価金額(80-87): {second_half[78:86]}')
        print(f'  売価(88-93): {second_half[86:92]}')
        print(f'  売価金額(94-101): {second_half[92:100]}') 