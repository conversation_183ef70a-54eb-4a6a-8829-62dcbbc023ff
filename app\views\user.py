from flask import Blueprint, jsonify, request, current_app, flash, redirect, url_for, render_template
from flask_login import login_required, current_user
from werkzeug.security import generate_password_hash
from app import db
from app.models import User

# Blueprintの作成
user = Blueprint('user', __name__, url_prefix='/api/users')

@user.route('/register', methods=['POST'])
@login_required
def register_user():
    if not current_user.is_approver:
        return jsonify({'error': '登録権限がありません'}), 403

    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return jsonify({'error': 'ユーザー名とパスワードは必須です'}), 400

        # ユーザー名の重複チェック
        existing_user = User.query.filter_by(username=username, is_deleted=0).first()
        if existing_user:
            return jsonify({'error': 'このユーザー名は既に使用されています'}), 400

        # 新しいユーザーを作成
        new_user = User(
            username=username,
            password_hash=generate_password_hash(password),
            is_approver=False,
            is_deleted=0,
            adminuser=False
        )
        db.session.add(new_user)
        db.session.commit()

        return jsonify({'message': 'ユーザーが登録されました'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"ユーザー登録中にエラーが発生: {str(e)}")
        return jsonify({'error': 'ユーザー登録中にエラーが発生しました'}), 500

@user.route('/change-password', methods=['POST'])
@login_required
def change_password():
    try:
        data = request.get_json()
        username = data.get('username')
        new_password = data.get('password')
        
        if not username or not new_password:
            return jsonify({'error': 'ユーザー名とパスワードは必須です'}), 400
            
        user = User.query.filter_by(username=username, is_deleted=0).first()
        if not user:
            return jsonify({'error': 'ユーザーが見つかりません'}), 404
            
        if not current_user.adminuser and current_user.username != username:
            return jsonify({'error': '他のユーザーのパスワードは変更できません'}), 403
            
        user.password_hash = generate_password_hash(new_password)
        db.session.commit()
        
        return jsonify({'message': 'パスワードが更新されました'})
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"パスワード更新中にエラーが発生: {str(e)}")
        return jsonify({'error': 'パスワード更新中にエラーが発生しました'}), 500

@user.route('/<int:user_id>/approver', methods=['PUT'])
@login_required
def update_user_approver(user_id):
    # 管理者権限チェック
    if not current_user.is_admin():
        return jsonify({'error': '管理者権限が必要です'}), 403
        
    try:
        data = request.get_json()
        is_approver = data.get('is_approver')
        
        user = User.query.get_or_404(user_id)
        
        # 管理者の承認権限は変更不可
        if user.is_admin():
            return jsonify({'error': '管理者の承認権限は変更できません'}), 400
            
        user.is_approver = is_approver
        db.session.commit()
        
        action = '付与' if is_approver else '解除'
        return jsonify({'message': f'承認権限を{action}しました。'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '承認権限の更新に失敗しました。'}), 500

@user.route('/api/users/<int:user_id>', methods=['DELETE'])
@login_required
def delete_user(user_id):
    if not current_user.is_admin():
        return jsonify({'error': '管理者権限が必要です'}), 403

    try:
        user_to_delete = User.query.get_or_404(user_id)
        
        # 管理者は自分自身を削除できない
        if user_to_delete.id == current_user.id:
            return jsonify({'error': '自分自身は削除できません'}), 400
            
        # 他の管理者は削除できない
        if user_to_delete.is_admin():
            return jsonify({'error': '管理者ユーザーは削除できません'}), 400

        user_to_delete.is_deleted = 1
        db.session.commit()
        
        return jsonify({'message': 'ユーザーを削除しました'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'ユーザーの削除に失敗しました'}), 500

@user.route('/', methods=['GET'])
@login_required
def get_users():
    if not current_user.is_approver:
        return jsonify({'error': '権限がありません。'}), 403
        
    try:
        users = User.query.filter_by(is_deleted=False).all()
        return jsonify([{
            'id': user.id,
            'username': user.username,
            'is_approver': user.is_approver
        } for user in users])
    except Exception as e:
        current_app.logger.error(f"ユーザー一覧取得エラー: {str(e)}")
        return jsonify({'error': 'ユーザー一覧の取得に失敗しました。'}), 500

@user.route('/settings', methods=['GET'])
@login_required
def settings():
    if not current_user.is_approver:
        flash('この画面にアクセスする権限がありません。')
        return redirect(url_for('auth.index'))
        
    users = User.query.all()
    return render_template('user_settings.html', users=users)

