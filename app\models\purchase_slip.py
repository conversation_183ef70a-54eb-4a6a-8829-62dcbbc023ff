from app import db
import json
from datetime import datetime
from app.models.item import Item
from app.models.section import Section

class PurchaseSlip(db.Model):
    __bind_key__ = 'OCRInvoice'
    __tablename__ = 'purchase_slip'
    __table_args__ = {'schema': 'dbo'}
    
    id = db.Column(db.Integer, primary_key=True)
    slip_number = db.Column(db.String(10), nullable=False)
    slip_date = db.Column(db.Date, nullable=False)
    company_code = db.Column(db.String(2), nullable=True)
    vendor_code = db.Column(db.String(10), nullable=True)
    vendor_name = db.Column(db.Unicode(50), nullable=True)
    store_code = db.Column(db.String(10), nullable=True)
    store_name = db.Column(db.Unicode(50), nullable=True)
    department_code = db.Column(db.String(3), nullable=True)
    cost_amount = db.Column(db.Numeric(18, 2), nullable=True)
    selling_amount = db.Column(db.Numeric(18, 2), nullable=True)
    tax_division_code = db.Column(db.String(2), nullable=True)
    tax_rate = db.Column(db.Numeric(3, 1), nullable=True)  # 消費税率（整数部2桁、小数部1桁）
    status = db.Column(db.String(10), default='pending')  # 'pending' or 'approved'
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    send_flg = db.Column(db.Boolean, default=False)
    approved_at = db.Column(db.DateTime)
    approved_by = db.Column(db.Integer, db.ForeignKey('dbo.users.id'))
    err_description = db.Column(db.String(255), nullable=True)
    is_deleted = db.Column(db.Integer, default=0, nullable=False)
    send_datetime = db.Column(db.DateTime, nullable=True)

    # リレーションシップ
    details = db.relationship(
        'PurchaseSlipDetail',
        backref='slip',
        lazy='joined',
        foreign_keys='PurchaseSlipDetail.slip_id'
    )
    approver = db.relationship('User', backref='approved_slips', lazy=True)

    @property
    def status_display(self):
        """ステータスの表示用文字列を返す"""
        return '承認済' if self.status == 'approved' else '未承認'

    @property
    def send_flg_display(self):
        """送信状態の表示用文字列を返す"""
        return '送信済' if self.send_flg else '未送信'

    def __repr__(self):
        return f'<PurchaseSlip {self.slip_number}>'

class PurchaseSlipDetail(db.Model):
    __bind_key__ = 'OCRInvoice'
    __tablename__ = 'purchase_slip_detail'
    __table_args__ = {
        'schema': 'dbo',
        'extend_existing': True
    }
    
    id = db.Column(db.Integer, primary_key=True)
    slip_id = db.Column(db.Integer, db.ForeignKey('dbo.purchase_slip.id'), nullable=False)
    line_number = db.Column(db.Integer, nullable=False)
    product_code = db.Column(db.String(14), nullable=False)
    product_name = db.Column(db.Unicode(100), nullable=True)
    unit = db.Column(db.String(8), nullable=True)
    quantity = db.Column(db.Numeric(18, 2), nullable=False)
    unit_cost = db.Column(db.Numeric(18, 2), nullable=False)
    unit_price = db.Column(db.Numeric(18, 2), nullable=False)
    amount = db.Column(db.Numeric(18, 2), nullable=False)
    item_id = db.Column(db.BigInteger, nullable=True)
    section_id = db.Column(db.BigInteger, nullable=True)
    
    # 原本情報
    org_product_code = db.Column(db.String(14), nullable=True)
    org_product_name = db.Column(db.Unicode(100), nullable=True)
    org_unit = db.Column(db.String(8), nullable=True)
    org_quantity = db.Column(db.Numeric(18, 2), nullable=True)
    org_unit_cost = db.Column(db.Numeric(18, 2), nullable=True)
    org_unit_price = db.Column(db.Numeric(18, 2), nullable=True)
    org_amount = db.Column(db.Numeric(18, 2), nullable=True)
    
    # Itemとのリレーションシップを修正
    item = db.relationship(
        'Item',
        primaryjoin='and_(foreign(PurchaseSlipDetail.item_id) == Item.id, Item.del_flg == 0)',
        lazy='select',  # 必要に応じてのみロード
        viewonly=True,  # 読み取り専用
        info={'bind_key': 'YszInventory'}
    )
    
    # Sectionとのリレーションシップを追加
    section = db.relationship(
        'Section',
        primaryjoin='and_(foreign(PurchaseSlipDetail.section_id) == Section.id, Section.del_flg == 0)',
        lazy='select',  # 必要に応じてのみロード
        viewonly=True,  # 読み取り専用
        info={'bind_key': 'YszInventory'}
    )

    def __init__(self, **kwargs):
        super(PurchaseSlipDetail, self).__init__(**kwargs)

class PurchaseSlipHistory(db.Model):
    __bind_key__ = 'OCRInvoice'
    __tablename__ = 'purchase_slip_history'
    __table_args__ = {'schema': 'dbo'}
    
    id = db.Column(db.Integer, primary_key=True)
    slip_id = db.Column(db.Integer, db.ForeignKey('dbo.purchase_slip.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('dbo.users.id'), nullable=False)
    details = db.Column(db.Unicode(500), nullable=True)
    created_at = db.Column(db.DateTime, nullable=False, server_default=db.text('getdate()'))
    action = db.Column(db.Unicode(50), nullable=False)
    
    def __init__(self, **kwargs):
        if 'details' in kwargs and isinstance(kwargs['details'], dict):
            kwargs['details'] = json.dumps(kwargs['details'], ensure_ascii=False)
        super(PurchaseSlipHistory, self).__init__(**kwargs)
    
    user = db.relationship(
        'User',
        backref=db.backref('slip_histories', lazy=True),
        lazy='joined'
    )

