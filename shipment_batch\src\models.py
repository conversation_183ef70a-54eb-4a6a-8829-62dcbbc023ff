#!/usr/bin/env python3
"""
データモデル定義
"""
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from typing import List, Optional


@dataclass
class PurchaseSlip:
    """仕入伝票データモデル"""
    id: int
    slip_number: str
    slip_date: datetime
    company_code: Optional[str]
    vendor_code: Optional[str]
    vendor_name: Optional[str]
    store_code: Optional[str]
    store_name: Optional[str]
    store_name_kana: Optional[str]
    department_code: Optional[str]
    cost_amount: Optional[Decimal]
    selling_amount: Optional[Decimal]
    tax_division_code: Optional[str]
    tax_rate: Optional[Decimal]
    status: str
    created_at: datetime
    updated_at: datetime
    send_flg: bool
    approved_at: Optional[datetime]
    approved_by: Optional[int]
    err_description: Optional[str]
    is_deleted: int
    send_datetime: Optional[datetime]
    details: List['PurchaseSlipDetail'] = None

    def __post_init__(self):
        if self.details is None:
            self.details = []


@dataclass
class PurchaseSlipDetail:
    """仕入伝票明細データモデル"""
    id: int
    slip_id: int
    line_number: int
    product_code: str
    product_name: Optional[str]
    unit: Optional[str]
    quantity: Decimal
    unit_cost: Decimal
    unit_price: Decimal
    amount: Decimal
    item_id: Optional[int]
    section_id: Optional[int]
    
    # 原本情報
    org_product_code: Optional[str]
    org_product_name: Optional[str]
    org_unit: Optional[str]
    org_quantity: Optional[Decimal]
    org_unit_cost: Optional[Decimal]
    org_unit_price: Optional[Decimal]
    org_amount: Optional[Decimal]


@dataclass
class FileStatistics:
    """ファイル統計情報"""
    total_records: int = 0
    header_records: int = 0
    slip_header_records: int = 0
    detail_records: int = 0
    trailer_records: int = 0
    total_slips: int = 0
    total_amount: Decimal = Decimal('0')
    
    def add_slip(self, slip: PurchaseSlip):
        """伝票統計を追加"""
        self.total_slips += 1
        # detail_recordsは実際の出力時にカウントするため、ここではカウントしない
        if slip.cost_amount:
            self.total_amount += slip.cost_amount
    
    def finalize(self):
        """統計を確定"""
        # total_recordsは_write_256byte_lineで自動的にカウントされるため、
        # ここでは何もしない（既にカウント済み）
        pass 