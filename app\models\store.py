from app import db

class Store(db.Model):
    __bind_key__ = 'OCRInvoice'
    __tablename__ = 'stores'
    __table_args__ = {
        'schema': 'dbo',
        'extend_existing': True
    }
    
    id = db.Column(db.Integer, primary_key=True)
    store_code = db.Column(db.String(10), nullable=False, unique=True)
    store_name = db.Column(db.Unicode(50), nullable=False)
    store_name_kana = db.Column(db.String(50), nullable=True)  # 半角カタカナ店舗名
    created_at = db.Column(db.DateTime, nullable=True)
    updated_at = db.Column(db.DateTime, nullable=True)
    
    def __repr__(self):
        return f"<Store {self.store_code}: {self.store_name}>"
