from app import db

class Vendor(db.Model):
    __bind_key__ = 'OCRInvoice'
    __tablename__ = 'vendor'
    __table_args__ = {
        'schema': 'dbo',
        'extend_existing': True
    }
    
    id = db.Column(db.Integer, primary_key=True)
    vendor_code = db.Column(db.String(10), nullable=False, unique=True)
    vendor_name = db.Column(db.String(100), nullable=False)
    tax_type = db.Column(db.Integer, nullable=False, default=1)  # 1:原売価内税, 3:非課税, 5:原価外税売価外税
    tax_rate = db.Column(db.Numeric(3, 1), nullable=False, default=10.0)  # 消費税率（整数部2桁、小数部1桁）
    created_at = db.Column(db.DateTime, nullable=True)
    updated_at = db.Column(db.DateTime, nullable=True)
    
    @property
    def tax_type_code(self):
        """税区分を2桁コード形式で返す"""
        mapping = {
            1: '01',  # 原売価内税
            3: '03',  # 非課税
            5: '05'   # 原価外税売価外税
        }
        return mapping.get(self.tax_type, '01')
    
    @tax_type_code.setter
    def tax_type_code(self, value):
        """2桁コードから整数値に変換してtax_typeに設定"""
        mapping = {
            '01': 1,  # 原売価内税
            '03': 3,  # 非課税
            '05': 5   # 原価外税売価外税
        }
        self.tax_type = mapping.get(value, 1)
    
    def get_tax_type_display(self):
        """税区分の表示名を返す"""
        mapping = {
            1: '01:原売価内税',
            3: '03:非課税',
            5: '05:原価外税売価外税'
        }
        return mapping.get(self.tax_type, '01:原売価内税')
    
    def get_tax_rate_display(self):
        """消費税率の表示用フォーマット"""
        return f"{float(self.tax_rate):.1f}%"
    
    def __repr__(self):
        return f"<Vendor {self.vendor_code}: {self.vendor_name}>"
