{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">ログイン</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.login') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="username" class="form-label">ユーザー名</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">パスワード</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">ログイン</button>
                    {% with messages = get_flashed_messages() %}
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-danger mt-3" id="loginError">
                                    {% if message == 'Please log in to access this page' %}
                                        ログインが必要です
                                    {% else %}
                                        {{ message }}
                                    {% endif %}
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 上部のメッセージを非表示にする
        const topMessages = document.querySelectorAll('.container > .alert');
        topMessages.forEach(msg => msg.style.display = 'none');

        // ログインエラーメッセージのフェードアウト
        const loginError = document.getElementById('loginError');
        if (loginError) {
            setTimeout(function() {
                loginError.style.transition = 'opacity 0.5s';
                loginError.style.opacity = '0';
                setTimeout(function() {
                    loginError.style.display = 'none';
                }, 500);
            }, 2000);
        }
    });
</script>
{% endblock %}
