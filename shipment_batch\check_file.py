#!/usr/bin/env python3

with open('output/Shipment_20250526165048_000820.DAT', 'r', encoding='shift_jis') as f:
    lines = f.readlines()

print(f'Total lines: {len(lines)}')
if lines:
    print(f'Last line starts with: {lines[-1][:10]}...')
    print(f'Last line length: {len(lines[-1].rstrip())}')
    
    # NNN9で始まる行を探す
    trailer_lines = [i for i, line in enumerate(lines) if line.startswith('NNN9')]
    print(f'Trailer lines found at: {trailer_lines}') 