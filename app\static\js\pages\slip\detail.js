import { setupInputConverters } from '../../common/utils.js';
import { updateHistoryList, initializeHistory } from './history.js';
import { DetailRowManager } from './detailrow.js';

// 初期化フラグをグローバルに設定
let isInitialized = false;
let isApprovalButtonsInitialized = false;

// DOMContentLoaded イベントリスナーから不要な関数呼び出しを削除
document.addEventListener('DOMContentLoaded', function() {
    initializeSlipDetail();
    initializeApprovalButtons();
    initializeDeleteButton();
    initializeUnapproveButton();
    DetailRowManager.updateTotalAmounts(); // ページロード時に合計金額を計算
});

// 伝票詳細画面の初期化関数をエクスポート
export function initializeSlipDetail() {
    if (isInitialized) return;
    isInitialized = true;

    setupInputConverters();
    initializeEventListeners();
    DetailRowManager.initialize();
    initializeHistory();
    initializeApprovalButtons();
    initializeDeleteButton();
    initializeUnapproveButton();
}

document.addEventListener('DOMContentLoaded', initializeSlipDetail);

    // 入力フィールドのバリデーションと変換
    document.querySelectorAll('input[type="text"]').forEach(input => {
        input.addEventListener('blur', function() {
            this.value = convertFullToHalf(this.value);
        });
    });

// CSRFトークン取得関数
function getCsrfToken() {
    const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (!token) {
        console.error('CSRF token not found');
        throw new Error('CSRF token not found');
    }
    return token;
}

// メッセージ表示用のヘルパー関数
function showMessage(elementId, message, type = 'success', duration = 2000) {
    const messageElement = document.getElementById(elementId);
    if (messageElement) {
        messageElement.textContent = message;
        messageElement.className = `alert alert-${type}`;
        messageElement.style.display = 'block';

        // 2秒後にメッセージを非表示にする
        setTimeout(() => {
            messageElement.style.display = 'none';
        }, duration);
    }
}

// 入力値の全角→半角変換
function convertFullToHalf(str) {
    return str.replace(/[！-～]/g, function(s) {
        return String.fromCharCode(s.charCodeAt(0) - 0xFEE0);
    }).replace(/　/g, ' ');
}

// 数値を3桁カンマ区切りで円表示するフォーマット関数
function formatNumber(number) {
    return number.toLocaleString() ;
}

// 承認ボタンの初期化
function initializeApprovalButtons() {
    if (isApprovalButtonsInitialized) return;
    isApprovalButtonsInitialized = true;

    const approveButton = document.querySelector('.approve-button');
    if (approveButton) {
        approveButton.addEventListener('click', handleApprove);
    }
}

// 承認・承認取消の共通処理
function handleSlipApprovalAction(button, action) {
    return async function() {
        const slipId = button.dataset.slipId;
        
        try {
            if (!confirm(`この伝票を${action === 'approve' ? '承認' : '承認取消'}してもよろしいですか？`)) {
                return;
            }

            // ボタンを無効化して処理中表示
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm"></span> 処理中...';

            // バリデーションチェック（承認時のみ）
            if (action === 'approve') {
                const validateResponse = await fetch(`/slip/${slipId}/validate`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'}
                });
                
                const validateData = await validateResponse.json();
                if (!validateResponse.ok) {
                    showMessage(validateData.error_details || validateData.error, { type: 'danger' });
                    return;
                }
            }

            // 承認/承認取消処理
            const response = await fetch(`/slip/${slipId}/${action}`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                credentials: 'include'
            });

            const data = await response.json();

            if (response.ok) {
                alert(data.message);
                window.location.href = data.redirectUrl || window.location.reload();
            } else {
                alert(data.error || `${action === 'approve' ? '承認' : '承認取消'}処理に失敗しました`);
                resetButton();
            }
        } catch (error) {
            console.error('Error:', error);
            alert(`${action === 'approve' ? '承認' : '承認取消'}処理中にエラーが発生しました`);
            resetButton();
        }

        function resetButton() {
            button.disabled = false;
            button.textContent = action === 'approve' ? '承認' : '承認取消';
        }
    };
}

// 伝票番号の初期化関数
function initializeSlipNumberInput() {
    const slipNumberInput = document.querySelector('input[name="slip_number"]');
    if (!slipNumberInput || slipNumberInput.hasAttribute('data-initialized')) {
        return;
    }
    
    // 初期化済みフラグを設定
    slipNumberInput.setAttribute('data-initialized', 'true');
    slipNumberInput.dataset.originalValue = slipNumberInput.value;

    // 入力時のバリデーション（リアルタイムチェック）
    slipNumberInput.addEventListener('input', function() {
        // 半角数字以外を除去
        this.value = this.value.replace(/[^0-9]/g, '');
        // 先頭の0を除去
        this.value = this.value.replace(/^0+/, '');
        // 10桁を超える入力を制限
        if (this.value.length > 10) {
            this.value = this.value.slice(0, 10);
        }
    });

    slipNumberInput.addEventListener('change', async function(event) {
        // イベントが既に処理済みかチェック
        if (event.processed || this.dataset.processing === 'true') {
            return;
        }

        event.processed = true;
        this.dataset.processing = 'true';

        try {
        const slipId = document.querySelector('.slip-detail-form').dataset.slipId;
        const value = this.value;

        // 空値チェック
        if (!value) {
            alert('伝票番号を入力してください');
                this.value = this.dataset.originalValue;
            return;
        }

            const result = await updateSlipField(slipId, 'slip_number', value, headerFields.slip_number);
            if (result?.status === 'success') {
                this.dataset.originalValue = value;
            }
        } catch (error) {
            alert(error.message);
            this.value = this.dataset.originalValue;
        } finally {
            delete this.dataset.processing;
        }
    });
}

// 会社コードの更新処理
function initializeCompanyCodeInput() {
const companyCodeInput = document.querySelector('input[name="company_code"]');
    if (!companyCodeInput || companyCodeInput.hasAttribute('data-initialized')) {
        return;
    }

    // 初期化済みフラグを設定
    companyCodeInput.setAttribute('data-initialized', 'true');
    companyCodeInput.dataset.originalValue = companyCodeInput.value;

    companyCodeInput.addEventListener('change', async function(event) {
        // イベントが既に処理済みかチェック
        if (event.processed || this.dataset.processing === 'true') {
            return;
        }

        event.processed = true;
        this.dataset.processing = 'true';

        try {
            const slipId = document.querySelector('.slip-detail-form').dataset.slipId;
            const value = this.value;

            // 入力値の基本バリデーション
            if (value && !/^[0-9]{2}$/.test(value)) {
                alert('会社コードは2桁の数字で入力してください');
                this.value = this.dataset.originalValue;
                return;
            }

            const result = await updateSlipField(slipId, 'company_code', value, headerFields.company_code);
            if (result?.status === 'success') {
                this.dataset.originalValue = value;
                console.log('会社コードを更新しました');
            }
        } catch (error) {
            alert(error.message);
            this.value = this.dataset.originalValue;
        } finally {
            delete this.dataset.processing;
        }
    });
}

// 取引先コードの更新処理
function initializeVendorCodeInput() {
    const vendorCodeInput = document.querySelector('input[name="vendor_code"]');
    if (!vendorCodeInput || vendorCodeInput.hasAttribute('data-initialized')) {
        return;
    }

    // 初期化済みフラグを設定
    vendorCodeInput.setAttribute('data-initialized', 'true');
    vendorCodeInput.dataset.originalValue = vendorCodeInput.value;

    vendorCodeInput.addEventListener('change', async function(event) {
        // イベントが既に処理済みかチェック
        if (event.processed || this.dataset.processing === 'true') {
            return;
        }

        event.processed = true;
        this.dataset.processing = 'true';

        try {
        const slipId = document.querySelector('.slip-detail-form').dataset.slipId;
            const value = this.value;

            // 入力値の基本バリデーション
            if (value && !/^\d{4}$/.test(value)) {
                alert('取引先コードは4桁の数字で入力してください');
                this.value = this.dataset.originalValue;
                return;
            }

            const result = await updateSlipField(slipId, 'vendor_code', value, headerFields.vendor_code);
            if (result?.status === 'success') {
                this.dataset.originalValue = value;
                // 取引先名を更新
                if (result.vendor_name) {
                    const vendorNameInput = document.querySelector('input[name="vendor_name"]');
                    if (vendorNameInput) {
                        vendorNameInput.value = result.vendor_name;
                    }
                }
                // 税区分を更新（Vendorから自動設定）
                if (result.tax_division_code) {
                    const taxDivisionCodeInput = document.querySelector('input[name="tax_division_code"]');
                    if (taxDivisionCodeInput) {
                        taxDivisionCodeInput.value = result.tax_division_code;
                    }
                }
                // 消費税率を更新（Vendorから自動設定）
                if (result.tax_rate) {
                    const taxRateInput = document.querySelector('input[name="tax_rate"]');
                    if (taxRateInput) {
                        taxRateInput.value = result.tax_rate.toFixed(1) + '%';
                    }
                }
            }
        } catch (error) {
            alert(error.message);
            this.value = this.dataset.originalValue;
        } finally {
            delete this.dataset.processing;
        }
    });
}

// 日付の初期化関数
function initializeSlipDateInput() {
    const slipDateInput = document.querySelector('input[name="slip_date"]');
    if (!slipDateInput || slipDateInput.hasAttribute('data-initialized')) {
        return;
    }
    
    slipDateInput.setAttribute('data-initialized', 'true');
    slipDateInput.dataset.originalValue = slipDateInput.value;

    // 日付入力のバリデーションと更新処理
    slipDateInput.addEventListener('change', async function(event) {
        if (event.processed || this.dataset.processing === 'true') {
            return;
        }

        event.processed = true;
        this.dataset.processing = 'true';

        try {
            const slipId = document.querySelector('.slip-detail-form').dataset.slipId;
            const value = this.value;

            // 日付のバリデーション
            if (!value) {
                alert('日付を入力してください');
                this.value = this.dataset.originalValue;
                return;
            }

            const result = await updateSlipField(slipId, 'slip_date', value, headerFields.slip_date);
            if (result?.status === 'success') {
                this.dataset.originalValue = value;
            }
        } catch (error) {
            alert(error.message);
            this.value = this.dataset.originalValue;
        } finally {
            delete this.dataset.processing;
        }
    });
}

// 店舗コードの更新処理
function initializeStoreCodeInput() {
    const storeCodeInput = document.querySelector('input[name="store_code"]');
    if (!storeCodeInput || storeCodeInput.hasAttribute('data-initialized')) {
        return;
    }

    // 初期化済みフラグを設定
    storeCodeInput.setAttribute('data-initialized', 'true');
    storeCodeInput.dataset.originalValue = storeCodeInput.value;

    storeCodeInput.addEventListener('change', async function(event) {
        // イベントが既に処理済みかチェック
        if (event.processed || this.dataset.processing === 'true') {
            return;
        }

        event.processed = true;
        this.dataset.processing = 'true';

        try {
            const slipId = document.querySelector('.slip-detail-form').dataset.slipId;
            const value = this.value;

            // 入力値の基本バリデーション
            if (value && !/^\d{2}$/.test(value)) {
                alert('店舗コードは2桁の数字で入力してください');
                this.value = this.dataset.originalValue;
                return;
            }

            const result = await updateSlipField(slipId, 'store_code', value, headerFields.store_code);
            if (result?.status === 'success') {
                this.dataset.originalValue = value;
                // 店舗名を更新
                if (result.store_name) {
                    const storeNameInput = document.querySelector('input[name="store_name"]');
                    if (storeNameInput) {
                        storeNameInput.value = result.store_name;
                    }
                }
            }
        } catch (error) {
            alert(error.message);
            this.value = this.dataset.originalValue;
        } finally {
            delete this.dataset.processing;
        }
    });
}

// 課コードの更新処理
function initializeDepartmentCodeInput() {
    const departmentCodeInput = document.querySelector('input[name="department_code"]');
    if (!departmentCodeInput || departmentCodeInput.hasAttribute('data-initialized')) {
        return;
    }

    // 初期化済みフラグを設定
    departmentCodeInput.setAttribute('data-initialized', 'true');
    departmentCodeInput.dataset.originalValue = departmentCodeInput.value;

    departmentCodeInput.addEventListener('change', async function(event) {
        // イベントが既に処理済みかチェック
        if (event.processed || this.dataset.processing === 'true') {
            return;
        }

        event.processed = true;
        this.dataset.processing = 'true';

        try {
            const slipId = document.querySelector('.slip-detail-form').dataset.slipId;
            const value = this.value;

            // 入力値の基本バリデーション
            if (value && !/^\d{3}$/.test(value)) {
                alert('課コードは3桁の数字で入力してください');
                this.value = this.dataset.originalValue;
                return;
            }

            const result = await updateSlipField(slipId, 'department_code', value, headerFields.department_code);
            if (result?.status === 'success') {
                this.dataset.originalValue = value;
                // 課名を更新
                if (result.department_name) {
                    const departmentNameInput = document.querySelector('input[name="department_name"]');
                    if (departmentNameInput) {
                        departmentNameInput.value = result.department_name;
                    }
                }
            }
        } catch (error) {
            alert(error.message);
            this.value = this.dataset.originalValue;
        } finally {
            delete this.dataset.processing;
        }
    });
}

// 税区分の更新処理
function initializeTaxDivisionCodeInput() {
    const taxDivisionCodeInput = document.querySelector('input[name="tax_division_code"]');
    if (!taxDivisionCodeInput || taxDivisionCodeInput.hasAttribute('data-initialized')) {
        return;
    }

    // 初期化済みフラグを設定
    taxDivisionCodeInput.setAttribute('data-initialized', 'true');
    
    // 読み取り専用として設定し、フォーカス時にメッセージを表示
    taxDivisionCodeInput.addEventListener('focus', function() {
        alert('税区分は取引先コードから自動設定されます。取引先コードを変更してください。');
        this.blur();
    });

    // キーボード入力を無効化
    taxDivisionCodeInput.addEventListener('keydown', function(event) {
        event.preventDefault();
        alert('税区分は取引先コードから自動設定されます。');
    });

    // 変更イベントを無効化
    taxDivisionCodeInput.addEventListener('change', function(event) {
        event.preventDefault();
        event.stopPropagation();
        alert('税区分は取引先コードから自動設定されます。');
    });
}

// 消費税率の更新処理
function initializeTaxRateInput() {
    const taxRateInput = document.querySelector('input[name="tax_rate"]');
    if (!taxRateInput || taxRateInput.hasAttribute('data-initialized')) {
        return;
    }

    // 初期化済みフラグを設定
    taxRateInput.setAttribute('data-initialized', 'true');
    
    // 読み取り専用として設定し、フォーカス時にメッセージを表示
    taxRateInput.addEventListener('focus', function() {
        alert('消費税率は取引先コードから自動設定されます。取引先コードを変更してください。');
        this.blur();
    });

    // キーボード入力を無効化
    taxRateInput.addEventListener('keydown', function(event) {
        event.preventDefault();
        alert('消費税率は取引先コードから自動設定されます。');
    });

    // 変更イベントを無効化
    taxRateInput.addEventListener('change', function(event) {
        event.preventDefault();
        event.stopPropagation();
        alert('消費税率は取引先コードから自動設定されます。');
    });
}

// DOMContentLoadedイベントで初期化
document.addEventListener('DOMContentLoaded', () => {
    if (!window.isDetailInitialized) {
        window.isDetailInitialized = true;
        initializeSlipNumberInput();
        initializeSlipDateInput();
        initializeCompanyCodeInput();
        initializeVendorCodeInput();
        initializeStoreCodeInput();
        initializeDepartmentCodeInput();
        initializeTaxDivisionCodeInput();
        initializeTaxRateInput();       
    }
});

// 共通のフェッチ設定とエンドポイントのマッピング
const fetchConfig = {
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': getCsrfToken()
    },
    credentials: 'include'
};

// エンドポイントのマッピング（グローバルスコープに移動）
const headerFields = {
    slip_number: 'number/update',
    slip_date: 'date/update',
    company_code: 'company/update',
    vendor_code: 'vendor/update',
    store_code: 'store/update',
    department_code: 'department/update',
    tax_division_code: 'tax/update'
};

// initializeEventListeners 関数内の headerFields の定義を削除し、グローバルの headerFields を使用
function initializeEventListeners() {
    // すでに初期化済みの場合は処理をスキップ
    if (window.eventListenersInitialized) return;
    window.eventListenersInitialized = true;

    const slipDetailForm = document.querySelector('.slip-detail-form');
    const slipId = slipDetailForm?.dataset.slipId;
    if (!slipId) return;

    Object.entries(headerFields).forEach(([fieldName, endpoint]) => {
        const input = document.querySelector(`input[name="${fieldName}"]`);
        // データ属性を使用して初期化済みかどうかを確認
        if (input && !input.hasAttribute('data-initialized')) {
            input.setAttribute('data-initialized', 'true');
            input.dataset.originalValue = input.value;

            input.addEventListener('change', async function(event) {
                // イベントの伝播を停止（税区分の場合のみ）
                if (fieldName === 'tax_division_code') {
                    event.stopPropagation();
                }

                if (event.processed || this.dataset.processing === 'true') {
                    return;
                }

                event.processed = true;
                this.dataset.processing = 'true';

                try {
                    if (this.value.trim() === '') {
                        alert(`${fieldName}を入力してください`);
                        this.value = this.dataset.originalValue;
            return;
        }

                    const result = await updateSlipField(slipId, fieldName, this.value, endpoint);
                    if (!result) return;

                    if (result?.status === 'success') {
                        this.dataset.originalValue = this.value;
                        if (fieldName === 'vendor_code' && result.vendor_name) {
                            document.querySelector('input[name="vendor_name"]').value = result.vendor_name;
                            // 税区分も更新（Vendorから自動設定）
                            if (result.tax_division_code) {
                                const taxDivisionCodeInput = document.querySelector('input[name="tax_division_code"]');
                                if (taxDivisionCodeInput) {
                                    taxDivisionCodeInput.value = result.tax_division_code;
                                }
                            }
                            // 消費税率を更新（Vendorから自動設定）
                            if (result.tax_rate) {
                                const taxRateInput = document.querySelector('input[name="tax_rate"]');
                                if (taxRateInput) {
                                    taxRateInput.value = result.tax_rate.toFixed(1) + '%';
                                }
                            }
                        }
                    }
                } catch (error) {
                    alert(error.message);
                    this.value = this.dataset.originalValue;
                } finally {
                    delete this.dataset.processing;
                }
            });
        }
    });
}

// DOMContentLoadedイベントで一度だけ初期化を実行
document.addEventListener('DOMContentLoaded', () => {
    if (!window.isDetailInitialized) {
        window.isDetailInitialized = true;
        initializeEventListeners();
    }
});

// 伝票フィールド更新の共通関数
async function updateSlipField(slipId, fieldName, value, endpoint) {
    try {
        const response = await fetch(`/slip/${slipId}/${endpoint}`, {
            ...fetchConfig,
            method: 'PUT',
            body: JSON.stringify({ [fieldName]: value })
        });

        // レスポンスのContent-Typeをチェック
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            if (response.redirected || response.status === 401 || response.status === 403) {
                window.location.href = '/login';
                return;
            }
            throw new Error('セッションが切れている可能性があります。ページを更新してください。');
        }

        const result = await response.json();
        if (!response.ok || result.error) {
            throw new Error(result.error || 'ネットワーク接続エラー');
        }

        // 履歴一覧を更新
        await updateHistoryList(slipId);

        return result;
    } catch (error) {
        if (error instanceof SyntaxError && error.message.includes('Unexpected token')) {
            window.location.href = '/login';
            return;
        }
        throw error;
    }
}

// 削除ボタンの初期化
function initializeDeleteButton() {
    const deleteButton = document.getElementById('slip-delete-btn');
    if (!deleteButton) return;

    // イベントリスナーが重複して登録されないようにする
    deleteButton.removeEventListener('click', handleDelete);
    deleteButton.addEventListener('click', handleDelete);
}

async function handleDelete(event) {
    event.preventDefault();
    
    const slipId = this.dataset.slipId;
    if (!slipId) return;

    // 確認ダイアログを表示
    if (!confirm('この伝票を削除してもよろしいですか？')) {
        return;
    }

    try {
        const response = await fetch(`/slip/${slipId}/delete`, {
            method: 'POST',
            ...fetchConfig
        });

        const data = await response.json();
        
        if (response.ok) {
            alert(data.message);
            // 一覧画面へリダイレクト
            window.location.href = '/slip_list';
        } else {
            alert(data.error || '削除処理に失敗しました。');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('削除処理中にエラーが発生しました。');
    }
}

function initializeUnapproveButton() {
    const unapproveButton = document.querySelector('.unapprove-btn');
    if (!unapproveButton) return;

    // イベントリスナーが重複して登録されないように
    unapproveButton.removeEventListener('click', handleUnapprove);
    unapproveButton.addEventListener('click', handleUnapprove);
}

async function handleUnapprove(event) {
    event.preventDefault();
    
    const slipId = this.dataset.slipId;
    if (!slipId) return;

    // 確認ダイアログを一度だけ表示
    if (!confirm('この伝票の承認を取消してもよろしいですか？')) {
        return;
    }

    try {
        const response = await fetch(`/slip/${slipId}/unapprove`, {
            method: 'POST',
            ...fetchConfig
        });

        const data = await response.json();
        
        if (response.ok) {
            if (data.error && data.error.includes('送信済')) {
                alert('送信済の伝票は承認取消できません。');
            } else {
                alert(data.message || '承認を取り消しました。');
                window.location.href = '/slip_list';
            }
        } else {
            alert(data.error || '承認取消に失敗しました。');
        }
    } catch (error) {
        console.error('承認取消エラー:', error);
        alert('承認取消処理中にエラーが発生しました。');
    }
}

async function handleApprove(event) {
    const button = event.currentTarget;
    const slipId = button.dataset.slipId;
    const nextSlipId = button.dataset.nextSlipId;
    const noConfirm = button.dataset.noConfirm === 'true';

    try {
        // バリデーションチェック
        const validateResponse = await fetch(`/slip/${slipId}/validate`, {
            method: 'POST',
            ...fetchConfig
        });
        const validateResult = await validateResponse.json();

        // HTTPステータスコードが409の場合（課コード不一致の警告）
        if (validateResponse.status === 409) {
            if (confirm(validateResult.message)) {
                // ユーザーが確認した場合、承認処理を続行
                await processApproval(slipId, nextSlipId, noConfirm);
            }
            return;
        }

        // バリデーションエラーがある場合
        if (validateResponse.status === 400) {
            alert(validateResult.error_details || validateResult.error);
            return;
        }

        // バリデーションOKの場合、承認処理を実行
        await processApproval(slipId, nextSlipId, noConfirm);

    } catch (error) {
        console.error('承認処理中にエラーが発生:', error);
        alert('承認処理中にエラーが発生しました');
    }
}

// 承認処理の実行
async function processApproval(slipId, nextSlipId, noConfirm) {
    try {
        const response = await fetch(`/slip/${slipId}/approve`, {
            method: 'POST',
            ...fetchConfig
        });
        const result = await response.json();

        if (result.status === 'success') {
            alert('承認が完了しました');
            if (nextSlipId && !noConfirm) {
                if (confirm('次の未承認伝票に移動しますか？')) {
                    window.location.href = `/slip/${nextSlipId}`;
                    return;
                }
            }
            window.location.reload();
        } else {
            alert(result.error || '承認に失敗しました');
        }
    } catch (error) {
        console.error('承認処理中にエラーが発生:', error);
        alert('承認処理中にエラーが発生しました');
    }
}