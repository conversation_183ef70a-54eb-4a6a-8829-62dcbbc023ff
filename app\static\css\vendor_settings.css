/* vendor_settings.html 専用のスタイル */

/* ページ全体のコンテナ */
.vendor-settings-page {
    max-width: 1200px;
    margin: 0 auto;
}

/* 新規登録フォームの調整 */
.vendor-settings-page input[name="vendor_code"] {
    max-width: 120px;
}

.vendor-settings-page input[name="vendor_name"] {
    max-width: 400px;
}

.vendor-settings-page select[name="tax_type"] {
    max-width: 150px;
}

/* テーブルのカラム幅調整 */
.vendor-settings-page .table th:nth-child(1),
.vendor-settings-page .table td:nth-child(1) {
    width: 120px;
}

.vendor-settings-page .table th:nth-child(2),
.vendor-settings-page .table td:nth-child(2) {
    width: auto;
}

.vendor-settings-page .table th:nth-child(3),
.vendor-settings-page .table td:nth-child(3) {
    width: 100px;
    text-align: center;
}

.vendor-settings-page .table th:nth-child(4),
.vendor-settings-page .table td:nth-child(4) {
    width: 280px;
    text-align: right;
}

/* 編集モードの入力フィールド */
.vendor-settings-page .edit-name {
    max-width: 100%;
}

.vendor-settings-page .edit-tax-type {
    max-width: 100%;
}

/* ボタングループの調整 */
.vendor-settings-page .btn-sm {
    margin-left: 5px;
}

/* レスポンシブ対応 */
@media (max-width: 768px) {
    .vendor-settings-page .table td:nth-child(3),
    .vendor-settings-page .table td:nth-child(4) {
        text-align: left;
    }
    
    .vendor-settings-page .btn-sm {
        margin: 2px;
    }
} 