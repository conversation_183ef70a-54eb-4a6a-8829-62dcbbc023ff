# 出荷データ出力バッチ仕様書

## 概要
仕入伝票データから固定長ファイル（Shipment_yyyymmddhhmiss.DAT）を生成するバッチプログラム

## ファイルレイアウト仕様

### 1. ファイルヘッダー（NNN0）
| 項目名 | 型 | 桁数 | 内容 |
|--------|----|----|------|
| データ種別 | 文字列 | 2 | "NN" |
| レコード種別 | 文字列 | 2 | "N0" |
| FILLER1 | 文字列 | 20 | 空白埋め |
| ファイル作成日 | 文字列 | 8 | システム日付（YYYYMMDD） |
| ファイル作成時分秒 | 文字列 | 6 | システム時刻（HHMISS） |
| データ送信先 | 文字列 | 7 | 取引先コード（前0埋め） |
| データ送信元 | 文字列 | 6 | "000001" |
| レコード長 | 整数 | 3 | 128 |
| FILLER2 | 文字列 | 69 | 空白埋め |
| データシリアルNo | 整数 | 5 | 連番（前0埋め） |

### 2. 伝票ヘッダー（NNN1）
| 項目名 | 型 | 桁数 | 内容 | データソース |
|--------|----|----|------|-------------|
| データ種別 | 文字列 | 2 | "NN" | 固定値 |
| レコード種別 | 文字列 | 2 | "N1" | 固定値 |
| 小売店舗コード | 文字列 | 7 | 000+company_code+store_code | purchase_slip.company_code + purchase_slip.store_code |
| 企業名 | 文字列 | 20 | company_code（2桁）+半角スペース（18桁） | purchase_slip.company_code |
| 店舗名 | 文字列 | 20 | 店舗名カナ | stores.store_name_kana |
| 分類コード | 文字列 | 4 | 部門コード（前ゼロ埋め） | purchase_slip.department_code |
| 伝票番号 | 文字列 | 9 | 伝票番号（前ゼロ埋め） | purchase_slip.slip_number |
| 仕入先コード | 文字列 | 7 | 取引先コード（前ゼロ埋め） | purchase_slip.vendor_code |
| 社店コード | 文字列 | 12 | company_code+store_code（前ゼロ埋め） | purchase_slip.company_code + purchase_slip.store_code |
| 伝票区分 | 文字列 | 2 | "01" | 固定値 |
| 取引先コード | 文字列 | 8 | 取引先コード（前ゼロ埋め） | purchase_slip.vendor_code |
| 便区分 | 文字列 | 3 | "001" | 固定値 |
| FILLER | 文字列 | 13 | 半角スペース | 固定値 |
| 出力日付区分 | 文字列 | 1 | 半角スペース | 固定値 |
| 発注日 | 文字列 | 6 | 半角スペース | 固定値 |
| 納品予定日 | 文字列 | 6 | 伝票日付（YYMMDD） | purchase_slip.slip_date |
| 発注区分 | 文字列 | 1 | 半角スペース | 固定値 |
| データシリアルNo | 整数 | 5 | 連番（前0埋め） | 128バイト単位のレコード出力連番 |

### 3. 明細（NNN4）
| 項目名 | 型 | 桁数 | 内容 | データソース |
|--------|----|----|------|-------------|
| データ種別 | 文字列 | 2 | "NN" | 固定値 |
| レコード種別 | 文字列 | 2 | "N4" | 固定値 |
| FILLER1 | 文字列 | 5 | 半角スペース | 固定値 |
| 行No | 整数 | 2 | 同一伝票内連番（01から昇順） | purchase_slip_detail.line_number |
| 商品コード | 文字列 | 14 | 商品コード | purchase_slip_detail.product_code |
| 商品名 | 文字列 | 35 | 半角スペース | 固定値 |
| 発注単位 | 文字列 | 4 | "0001" | 固定値 |
| 単位区分 | 文字列 | 2 | 半角スペース | 固定値 |
| 発注数量 | 整数 | 6 | 数量（前0埋め） | purchase_slip_detail.quantity |
| 入荷予定数 | 整数 | 6 | 数量（前0埋め） | purchase_slip_detail.quantity |
| 原単価 | 整数 | 6 | 仕入単価（前0埋め） | purchase_slip_detail.unit_cost |
| 原価金額 | 整数 | 6 | 仕入金額（前0埋め、四捨五入） | unit_cost × quantity |
| 売価 | 整数 | 6 | 売価単価（前0埋め） | purchase_slip_detail.unit_price |
| 売価金額 | 整数 | 8 | 売価金額（前0埋め、四捨五入） | unit_price × quantity |
| 単位当たり入数 | 文字列 | 2 | "01" | 固定値 |
| ケース代 | 文字列 | 4 | "0000" | 固定値 |
| クラス | 文字列 | 8 | 半角スペース | 固定値 |
| 発注区分 | 文字列 | 1 | 半角スペース | 固定値 |
| 訂正理由区分 | 文字列 | 1 | 半角スペース | 固定値 |
| FILLER2 | 文字列 | 3 | 半角スペース | 固定値 |
| データシリアルNo | 整数 | 5 | 連番（前0埋め） | 128バイト単位のレコード出力連番 |

### 4. トレーラー（NNN9）
| 項目名 | 型 | 桁数 | 内容 |
|--------|----|----|------|
| データ種別 | 文字列 | 2 | "NN" |
| レコード種別 | 文字列 | 2 | "N9" |
| 固定値 | 文字列 | 21 | "999999999999999999999" |
| 配信レコード件数 | 整数 | 6 | ファイル中の128バイト単位での出力レコード数（前0埋め） |
| 配信アイテム件数 | 整数 | 6 | NN4のアイテムとして出力した128バイト単位のレコード数（前0埋め） |
| FILLER2 | 文字列 | 86 | 空白埋め |
| データシリアルNo | 整数 | 5 | 128バイト単位のレコード出力連番（前0埋め） |

**注意**: 最終レコードの256バイトに、先頭がNNN9の128バイトの場合、残りの256バイト目までは半角スペースで埋めて、256バイトの改行とする。

## ファイル出力仕様

### ファイル名
- 形式: `Shipment_yyyymmddhhmiss.DAT`
- 例: `Shipment_20250526125939.DAT`

### ファイル形式
- 文字コード: Shift-JIS
- レコード長: 256文字（128文字×2バイト）
- 改行: CRLF

### ファイル構造
1. 1行目: ファイルヘッダー（128文字）+ 最初の伝票ヘッダー（128文字）
2. 2行目以降: 明細レコード、伝票ヘッダー、トレーラーが順次出力
3. 最終行: トレーラー（256文字）

## データ抽出条件

### 対象データ
```sql
SELECT * FROM purchase_slip ps
LEFT JOIN purchase_slip_detail psd ON ps.id = psd.slip_id
LEFT JOIN stores s ON ps.store_code = s.store_code
WHERE ps.is_deleted = 0
  AND ps.send_flg = 0
  AND ps.approved_by IS NOT NULL
ORDER BY ps.id, psd.line_number
```

### 更新処理
出力完了後、以下の更新を実行：
```sql
UPDATE purchase_slip 
SET send_flg = 1, send_datetime = GETDATE()
WHERE id IN (出力対象の伝票ID)
```

## 実行環境
- OS: Windows Server
- Python: 3.8以上
- データベース: SQL Server
- 実行方式: スケジューラー実行

## 関連ファイル
- メインプログラム: `src/shipment_batch.py`
- 設定ファイル: `config/config.ini`
- 実行スクリプト: `run_batch_jp.bat`
- ログ出力先: `logs/shipment_batch.log` 