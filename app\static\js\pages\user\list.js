import { showMessage } from './settings.js';

// CSRFトークンの取得
function getCsrfToken() {
    return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
}

// ユーザー一覧の取得と表示
async function loadUserList() {
    const userList = document.getElementById('userList');
    const userListContainer = document.getElementById('userListContainer');
    const userListMessage = document.getElementById('userListMessage');
    
    if (!userList || !userListContainer) return;

    try {
        const response = await fetch('/api/users', {
            headers: {
                'X-CSRF-TOKEN': getCsrfToken()
            }
        });
        
        if (!response.ok) {
            const error = await response.json();
            if (response.status === 403) {
                // 権限がない場合はユーザー一覧セクション全体を非表示
                userListContainer.style.display = 'none';
                return;
            }
            throw new Error(error.error || 'ユーザー一覧の取得に失敗しました');
        }

        const users = await response.json();
        if (!Array.isArray(users)) {
            throw new Error('サーバーからの応答が不正です。管理者に連絡してください。');
        }

        if (users.length === 0) {
            userList.innerHTML = '<tr><td colspan="3" class="text-center">ユーザーが存在しません。</td></tr>';
            showMessage('userListMessage', 'ユーザーが登録されていません。', 'info', 0);
            return;
        }

        userList.innerHTML = users.map(user => `
            <tr>
                <td class="username">${user.username}</td>
                <td>
                    <div class="form-check">
                        <input class="form-check-input user-approver-checkbox" 
                               type="checkbox" 
                               ${user.is_approver ? 'checked' : ''} 
                               data-user-id="${user.id}"
                               ${!window.isAdmin || user.adminuser ? 'disabled' : ''}>
                    </div>
                </td>
                <td>
                    <button class="btn btn-sm btn-danger delete-user-btn" 
                            data-user-id="${user.id}"
                            ${user.username === currentUser || user.adminuser || !window.isAdmin ? 'disabled' : ''}>
                        削除
                    </button>
                </td>
            </tr>
        `).join('');

        // イベントリスナーの設定
        initializeUserListEvents();
    } catch (error) {
        console.error('Error:', error);
        showMessage('userListMessage', error.message || 'ユーザー一覧の読み込みに失敗しました。', 'danger');
    }
}

// 承認権限の切り替え処理
async function handleApproverToggle(event) {
    const checkbox = event.target;
    const userId = checkbox.dataset.userId;
    const isApprover = checkbox.checked;

    try {
        const response = await fetch(`/api/users/${userId}/approver`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': getCsrfToken()
            },
            body: JSON.stringify({ is_approver: isApprover })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '承認権限の更新に失敗しました。');
        }

        showMessage('userListMessage', '承認権限を更新しました。', 'success');

    } catch (error) {
        console.error('Error:', error);
        // エラー時はチェックボックスを元の状態に戻す
        checkbox.checked = !isApprover;
        showMessage('userListMessage', error.message, 'danger');
    }
}

// ユーザー削除の処理
async function handleUserDelete(event) {
    const button = event.target;
    const userId = button.dataset.userId;
    const username = button.closest('tr').querySelector('.username').textContent;

    // 自分自身の削除を試みた場合の追加チェック
    if (username === currentUser) {
        showMessage('userListMessage', '自分自身は削除できません。', 'danger');
        return;
    }

    if (!confirm('このユーザーを削除してもよろしいですか？')) {
        return;
    }

    try {
        const response = await fetch(`/api/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': getCsrfToken()
            }
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'ユーザーの削除に失敗しました。');
        }

        showMessage('userListMessage', 'ユーザーを削除しました。', 'success');
        loadUserList();  // ユーザー一覧を再読み込み

    } catch (error) {
        console.error('Error:', error);
        showMessage('userListMessage', error.message, 'danger');
    }
}

// ユーザー一覧のイベント初期化
function initializeUserListEvents() {
    // 承認権限の切り替えイベント
    document.querySelectorAll('.user-approver-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', handleApproverToggle);
    });

    // ユーザー削除ボタンのイベント
    document.querySelectorAll('.delete-user-btn').forEach(button => {
        button.addEventListener('click', handleUserDelete);
    });
}

export { loadUserList, initializeUserListEvents };
