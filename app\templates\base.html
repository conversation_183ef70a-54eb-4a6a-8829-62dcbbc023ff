<!DOCTYPE html>
<html lang="ja" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}OCR仕入伝票管理{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom.css') }}">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="{{ url_for('static', filename='js/common/theme.js') }}"></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    {% block head %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('auth.index') }}">OCR仕入伝票管理</a>
            {% if current_user.is_authenticated %}
            <div class="navbar-nav me-auto">
                {% if current_user.is_approver %}
                    <a class="nav-link" href="{{ url_for('vendor.settings') }}">OCR取引先設定</a>
                    <a class="nav-link" href="{{ url_for('store.settings') }}">店舗マスタ設定</a>
                    <a class="nav-link" href="{{ url_for('user.settings') }}">ユーザー設定</a>
                {% endif %}
            </div>
            <div class="navbar-nav">
                <button class="btn btn-outline-light me-2" id="theme-toggle">
                    <span class="theme-icon-light d-none">🌞</span>
                    <span class="theme-icon-dark">🌙</span>
                </button>
                <a class="nav-link" href="{{ url_for('auth.logout') }}">ログアウト</a>
            </div>
            {% endif %}
        </div>
    </nav>

    <main class="container my-4">
        <div class="container mt-4">
            {% if request.endpoint != 'main.login' %}
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-danger">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            {% endif %}
            {% block content %}{% endblock %}
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
