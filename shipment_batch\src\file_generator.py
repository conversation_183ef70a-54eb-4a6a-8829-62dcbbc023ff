#!/usr/bin/env python3
"""
ファイル生成クラス
"""
import logging
import os
import shutil
from datetime import datetime
from typing import List

from models import PurchaseSlip, PurchaseSlipDetail, FileStatistics
from utils import Config<PERSON>ana<PERSON>, <PERSON>Formatter, FileNameGenerator


class FileGenerator:
    """ファイル生成クラス"""
    
    def __init__(self, config: ConfigManager, db_manager=None):
        """
        Args:
            config: 設定管理インスタンス
            db_manager: データベース管理インスタンス
        """
        self.config = config
        self.output_config = config.get_output_config()
        self.system_config = config.get_system_config()
        self.formatter = RecordFormatter()
        self.statistics = FileStatistics()
        self.serial_number = 1
        self.last_detail_serial = 0  # 最後のNNN4レコードのシリアル番号を記録
        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager
    
    def generate_shipment_file(self, slips: List[PurchaseSlip], timestamp: datetime = None, vendor_code: str = None) -> str:
        """出荷ファイルを生成
        
        Args:
            slips: 仕入伝票リスト
            timestamp: ファイル生成タイムスタンプ
            vendor_code: ベンダーコード
        
        Returns:
            生成されたファイルのパス
        """
        try:
            if timestamp is None:
                timestamp = datetime.now()
            
            # vendor_codeが指定されていない場合は、最初の伝票から取得
            if vendor_code is None and slips:
                vendor_code = slips[0].vendor_code
            
            # ファイル名生成
            filename = FileNameGenerator.generate_shipment_filename(timestamp, vendor_code)
            output_path = os.path.join(self.output_config['output_path'], filename)
            temp_path = output_path + '.tmp'
            
            # 出力ディレクトリ作成
            os.makedirs(self.output_config['output_path'], exist_ok=True)
            os.makedirs(self.output_config['backup_path'], exist_ok=True)
            
            # 統計情報初期化（各ファイル生成時にリセット）
            self.statistics = FileStatistics()
            self.statistics.total_records = 0  # 128バイト単位でのレコード数を0から開始
            self.statistics.detail_records = 0  # NN4レコード数を0から開始
            self.serial_number = 1
            self.last_detail_serial = 0  # 最後のNNN4レコードのシリアル番号をリセット
            
            # 一時ファイルに出力
            with open(temp_path, 'w', encoding=self.system_config['encoding'], errors='replace') as f:
                # ファイルヘッダー作成（1ファイルに1つ）
                header_record = self.create_file_header(slips, timestamp)
                
                # 最初の伝票のヘッダーと連結
                first_slip = slips[0] if slips else None
                if first_slip:
                    self.statistics.add_slip(first_slip)
                    first_slip_header = self.create_slip_header(first_slip, timestamp)
                    # ファイルヘッダー + 最初の伝票ヘッダーを連結して出力
                    combined_header = header_record.ljust(128)[:128] + first_slip_header.ljust(128)[:128]
                    self._write_256byte_line(f, combined_header)
                    
                                    # 最初の伝票の明細出力
                pending_record = None  # 前半の128バイトレコードを保持
                for line_no, detail in enumerate(first_slip.details, 1):
                    detail_record = self.create_detail_record(detail, first_slip, line_no)
                    if pending_record is None:
                        pending_record = detail_record.ljust(128)[:128]
                        self.logger.debug(f"Set pending_record: detail {line_no}")
                    else:
                        # 前のレコードと組み合わせて256バイトで出力
                        combined_record = pending_record + detail_record.ljust(128)[:128]
                        self._write_256byte_line(f, combined_record)
                        pending_record = None
                        self.logger.debug(f"Combined and cleared pending_record: detail {line_no}")
                    # NN4レコード（明細）の統計カウント
                    self.statistics.detail_records += 1
                
                # 残りの伝票の処理
                for slip in slips[1:]:
                    # 統計情報更新
                    self.statistics.add_slip(slip)
                    
                    # 伝票ヘッダー処理
                    slip_header = self.create_slip_header(slip, timestamp)
                    if pending_record is None:
                        pending_record = slip_header.ljust(128)[:128]
                        self.logger.debug(f"Set pending_record: slip header {slip.slip_number}")
                    else:
                        # 前のレコードと組み合わせて256バイトで出力
                        combined_record = pending_record + slip_header.ljust(128)[:128]
                        self._write_256byte_line(f, combined_record)
                        pending_record = None
                        self.logger.debug(f"Combined and cleared pending_record: slip header {slip.slip_number}")
                    
                    # 明細出力
                    for line_no, detail in enumerate(slip.details, 1):
                        detail_record = self.create_detail_record(detail, slip, line_no)
                        if pending_record is None:
                            pending_record = detail_record.ljust(128)[:128]
                            self.logger.debug(f"Set pending_record: detail {slip.slip_number}-{line_no}")
                        else:
                            # 前のレコードと組み合わせて256バイトで出力
                            combined_record = pending_record + detail_record.ljust(128)[:128]
                            self._write_256byte_line(f, combined_record)
                            pending_record = None
                            self.logger.debug(f"Combined and cleared pending_record: detail {slip.slip_number}-{line_no}")
                        # NN4レコード（明細）の統計カウント
                        self.statistics.detail_records += 1
                
                # 統計確定
                self.statistics.finalize()
                
                # トレーラー出力（1ファイルに1つ）
                trailer_record = self.create_trailer()
                
                # デバッグログ
                self.logger.debug(f"pending_record exists: {pending_record is not None}")
                
                # 最終レコードの256バイト処理
                if pending_record is not None:
                    # 保留中のレコードがある場合、トレーラーと組み合わせる
                    self.logger.debug("Combining pending record with trailer")
                    trailer_with_pending = pending_record + trailer_record.ljust(128)[:128]
                    self._write_256byte_line(f, trailer_with_pending)
                else:
                    # トレーラーが前半の128バイトの場合、後半は半角スペースで埋める
                    self.logger.debug("Writing trailer with padding")
                    trailer_with_padding = trailer_record.ljust(128)[:128] + (" " * 128)
                    self._write_256byte_line(f, trailer_with_padding)
            
            # 一時ファイルを本ファイルに移動
            shutil.move(temp_path, output_path)
            
            # ファイル検証
            if not self.validate_file_format(output_path):
                raise Exception("生成されたファイルの形式が正しくありません")
            
            self.logger.info(f"出荷ファイル生成完了: {filename}")
            return output_path
            
        except Exception as e:
            # 一時ファイルのクリーンアップ
            if os.path.exists(temp_path):
                os.remove(temp_path)
            
            self.logger.error(f"出荷ファイル生成エラー: {e}")
            raise
    
    def create_file_header(self, slips: List[PurchaseSlip], timestamp: datetime, vendor_code: str = None) -> str:
        """ファイルヘッダーを作成
        
        Args:
            slips: 仕入伝票リスト
            timestamp: タイムスタンプ
            vendor_code: ベンダーコード（指定されない場合は最初の伝票から取得）
        
        Returns:
            ファイルヘッダーレコード
        """
        # vendor_codeが指定されていない場合は最初の伝票から取得
        if vendor_code is None:
            vendor_code = slips[0].vendor_code if slips else ""
        
        record = ""
        record += self.formatter.format_string("NN", 2)  # データ種別
        record += self.formatter.format_string("N0", 2)  # レコード種別
        record += self.formatter.format_string("", 20)   # FILLER1（20桁）
        record += self.formatter.format_date(timestamp, '%Y%m%d')  # ファイル作成日（8桁、YYYYMMDD）
        record += self.formatter.format_date(timestamp, '%H%M%S')  # ファイル作成時分秒（6桁、HHMISS）
        record += self.formatter.format_string(vendor_code or "", 7, 'R', '0')  # データ送信先（取引先コード、7桁、前0埋め）
        record += self.formatter.format_string("000001", 6)  # データ送信元（6桁、固定値）
        record += self.formatter.format_number(128, 3)  # レコード長（3桁、固定値128）
        record += self.formatter.format_string("", 69)   # FILLER2（69桁）
        record += self.formatter.format_number(self._get_next_serial(), 5)  # データシリアルNo
        
        return record
    
    def create_slip_header(self, slip: PurchaseSlip, timestamp: datetime) -> str:
        """伝票ヘッダーを作成
        
        Args:
            slip: 仕入伝票データ
            timestamp: タイムスタンプ
        
        Returns:
            伝票ヘッダーレコード
        """
        # 小売店舗コード（000 + company_code + store_code、7桁）
        retail_store_code = "000" + (slip.company_code or "01") + (slip.store_code or "0000")
        
        # 企業名（company_code 2桁 + 半角スペース18桁）
        company_name = (slip.company_code or "01") + (" " * 18)
        
        # 店舗名（stores.store_name_kana）
        store_name_kana = slip.store_name_kana or self.formatter.convert_to_kana(slip.store_name or "")
        
        # 社店コード（company_code + store_code、4桁）
        company_store_code = (slip.company_code or "01") + (slip.store_code or "00")
        
        record = ""
        record += self.formatter.format_string("NN", 2)  # データ種別
        record += self.formatter.format_string("N1", 2)  # レコード種別
        record += self.formatter.format_string(retail_store_code, 7)  # 小売店舗コード（7桁）
        record += self.formatter.format_string(company_name, 20)  # 企業名（2桁+スペース18桁）
        record += self.formatter.format_string(store_name_kana, 20)   # 店舗名（stores.store_name_kana）
        record += self.formatter.format_string(slip.department_code or "0000", 4, 'R', '0')  # 分類コード（前ゼロ埋め4桁）
        record += self.formatter.format_string(slip.slip_number or "", 9, 'R', '0')  # 伝票番号（前ゼロ埋め、9桁）
        record += self.formatter.format_string(slip.vendor_code or "", 7, 'R', '0')  # 仕入先コード（前ゼロ埋め、7桁）
        record += self.formatter.format_string(company_store_code, 12, 'R', '0')  # 社店コード（前ゼロ埋め12桁）
        record += self.formatter.format_string("01", 2)  # 伝票区分（固定値01、2桁）
        record += self.formatter.format_string(slip.vendor_code or "", 8, 'R', '0')  # 取引先コード（前ゼロ埋め、8桁）
        record += self.formatter.format_string("001", 3)  # 便区分（固定値001）
        record += self.formatter.format_string(" " * 13, 13)   # FILLER（半角スペース13桁）
        record += self.formatter.format_string(" ", 1)   # 出力日付区分（半角スペース1桁）
        record += self.formatter.format_string(" " * 6, 6)  # 発注日（半角スペース6桁）
        record += self.formatter.format_date(slip.slip_date, '%y%m%d')  # 納品予定日（YYMMDD）
        record += self.formatter.format_string(" ", 1)  # 発注区分（半角スペース1桁）
        record += self.formatter.format_number(self._get_next_serial(), 5)  # データシリアルNo（前0埋め5桁）
        
        return record
    
    def create_detail_record(self, detail: PurchaseSlipDetail, slip: PurchaseSlip, line_no: int) -> str:
        """明細レコードを作成
        
        Args:
            detail: 仕入伝票明細データ
            slip: 親の仕入伝票データ
            line_no: 同一伝票内での行番号（01から開始）
        
        Returns:
            明細レコード
        """
        # 数量と単価の取得（0の場合もあり得る）
        quantity = int(detail.quantity or 0)
        unit_cost = detail.unit_cost or 0
        unit_price = detail.unit_price or 0
        amount = detail.amount or 0
        
        # 原単価を100倍（小数点以下2桁を整数化）
        # Decimal型の場合も考慮して、floatに変換してから100倍
        unit_cost_100x = int(round(float(unit_cost) * 100))
        
        # 売価金額計算（quantity * unit_price）
        selling_amount = int(round(unit_price * quantity))
        
        record = ""
        record += self.formatter.format_string("NN", 2)  # データ種別
        record += self.formatter.format_string("N4", 2)  # レコード種別
        record += self.formatter.format_string(" " * 5, 5)  # FILLER1（半角スペース5桁固定）
        record += self.formatter.format_number(line_no, 2)  # 行No（前0埋め2桁昇順で01から同一伝票内で連番）
        record += self.formatter.format_string(detail.product_code or "", 14)  # 商品コード
        record += self.formatter.format_string(" " * 29, 29)  # 商品名（半角スペース29桁）
        record += self.formatter.format_number(quantity, 4)  # 発注単位（purchase_slip_detail.quantity、前0埋め4桁）
        record += self.formatter.format_string(" " * 2, 2)  # 単位区分（半角スペース2桁）
        record += self.formatter.format_number(quantity, 6)  # 発注数量（purchase_slip_detail.quantity、前0埋め6桁）
        record += self.formatter.format_number(quantity, 6)  # 入荷予定数（発注数量と同じ値、前0埋め6桁）
        record += self.formatter.format_number(unit_cost_100x, 8)  # 原単価（unit_cost×100、前0埋め8桁）
        record += self.formatter.format_number(int(amount), 8)  # 原価金額（purchase_slip_detail.amount、前0埋め8桁）
        record += self.formatter.format_number(int(unit_price), 6)  # 売価（purchase_slip_detail.unit_price、前0埋め6桁）
        record += self.formatter.format_number(selling_amount, 8)  # 売価金額（unit_price×quantity、前0埋め8桁）
        record += self.formatter.format_string("0001", 4)  # 単位当たり入数（0001固定、4桁）
        record += self.formatter.format_string("0000", 4)  # ケース代（0000固定、4桁）
        # クラス（半角スペース4桁＋section_code、8桁）
        section_code = "0000"  # デフォルト値
        if self.db_manager and detail.section_id:
            retrieved_section_code = self.db_manager.get_section_code(detail.section_id)
            if retrieved_section_code:
                # section_codeが4桁未満の場合は前0埋め、4桁を超える場合は右4桁を使用
                section_code = retrieved_section_code.zfill(4)[-4:]
        record += self.formatter.format_string(f"    {section_code}", 8)
        record += self.formatter.format_string(" " * 5, 5)  # FILLER（半角スペース5桁）
        
        # NNN4レコード（明細レコード）のシリアル番号を取得し、最後のものとして記録
        detail_serial = self._get_next_serial()
        self.last_detail_serial = detail_serial
        record += self.formatter.format_number(detail_serial, 5)  # データシリアルNo（前0埋め5桁）
        
        return record
    
    def create_trailer(self) -> str:
        """トレーラーを作成
        
        Returns:
            トレーラーレコード
        """
        # フッターレコードのデータシリアルNoを取得（自身のレコードも含めてカウント）
        trailer_serial_no = self._get_next_serial()
        
        record = ""
        record += self.formatter.format_string("NN", 2)  # データ種別
        record += self.formatter.format_string("N9", 2)  # レコード種別
        record += self.formatter.format_string("9" * 20, 20)  # 固定値「99999999999999999999」（20桁）
        # 配信レコード件数は、フッターレコードのデータシリアルNoと同じ値を設定
        record += self.formatter.format_number(trailer_serial_no, 6)  # 配信レコード件数（フッターのシリアルNoと同じ値）
        record += self.formatter.format_number(self.statistics.detail_records, 6) # 配信アイテム件数（NN4レコード数）
        record += self.formatter.format_string("", 87)   # FILLER2
        record += self.formatter.format_number(trailer_serial_no, 5)  # データシリアルNo
        
        return record
    
    def create_backup(self, original_file_path: str, timestamp: datetime = None) -> str:
        """バックアップファイルを作成
        
        Args:
            original_file_path: 元ファイルのパス
            timestamp: バックアップ作成タイムスタンプ
        
        Returns:
            バックアップファイルのパス
        """
        try:
            if not os.path.exists(original_file_path):
                raise FileNotFoundError(f"バックアップ対象ファイルが見つかりません: {original_file_path}")
            
            original_filename = os.path.basename(original_file_path)
            backup_filename = FileNameGenerator.generate_backup_filename(original_filename, timestamp)
            backup_path = os.path.join(self.output_config['backup_path'], backup_filename)
            
            shutil.copy2(original_file_path, backup_path)
            
            self.logger.info(f"バックアップ作成完了: {backup_filename}")
            return backup_path
            
        except Exception as e:
            self.logger.error(f"バックアップ作成エラー: {e}")
            raise
    
    def validate_file_format(self, file_path: str) -> bool:
        """ファイル形式の検証
        
        Args:
            file_path: 検証対象ファイルのパス
        
        Returns:
            検証結果
        """
        try:
            if not os.path.exists(file_path):
                self.logger.error(f"検証対象ファイルが見つかりません: {file_path}")
                return False
            
            with open(file_path, 'r', encoding=self.system_config['encoding'], errors='replace') as f:
                lines = f.readlines()
            
            if not lines:
                self.logger.error("ファイルが空です")
                return False
            
            # 各行のレコード長チェック
            expected_length = self.system_config['record_length']
            for i, line in enumerate(lines, 1):
                line_content = line.rstrip('\n\r')
                if len(line_content) != expected_length:
                    self.logger.error(f"レコード長エラー - 行{i}: 期待値={expected_length}, 実際={len(line_content)}")
                    return False
            
            # ファイル構造チェック
            if not self._validate_file_structure(lines):
                return False
            
            self.logger.info(f"ファイル形式検証完了: {os.path.basename(file_path)}")
            return True
            
        except Exception as e:
            self.logger.error(f"ファイル形式検証エラー: {e}")
            return False
    
    def get_file_statistics(self) -> FileStatistics:
        """ファイル統計情報を取得
        
        Returns:
            ファイル統計情報
        """
        return self.statistics
    
    def _get_next_serial(self) -> int:
        """次のシリアル番号を取得"""
        current = self.serial_number
        self.serial_number += 1
        return current
    
    def _get_delivery_type(self, slip: PurchaseSlip) -> str:
        """便区分を取得
        
        Args:
            slip: 仕入伝票データ
        
        Returns:
            便区分（3桁）
        """
        # 簡易的な便区分判定（実際の業務ロジックに応じて調整）
        if slip.cost_amount and slip.cost_amount > 10000:
            return "001"  # 特急便
        else:
            return "002"  # 通常便
    
    def _get_order_type(self, slip: PurchaseSlip) -> str:
        """発注区分を取得
        
        Args:
            slip: 仕入伝票データ
        
        Returns:
            発注区分（1桁）
        """
        # 簡易的な発注区分判定（実際の業務ロジックに応じて調整）
        return "1"  # 通常発注
    
    def _write_256byte_line(self, file_handle, record: str) -> None:
        """256バイト（128バイト×2レコード）で改行してファイルに書き込み
        
        Args:
            file_handle: ファイルハンドル
            record: 書き込むレコード（256バイト、または128バイト×2の形式）
        """
        # 256バイトに調整（不足分はスペースで埋める）
        adjusted_record = record.ljust(256)[:256]
        file_handle.write(adjusted_record + '\n')
        
        # 統計情報更新（128バイト単位でのレコード数をカウント）
        # 256バイトは128バイト×2なので、2レコード分としてカウント
        self.statistics.total_records += 2
    
    def _validate_file_structure(self, lines: List[str]) -> bool:
        """ファイル構造の検証
        
        Args:
            lines: ファイルの行リスト
        
        Returns:
            検証結果
        """
        try:
            if len(lines) < 2:
                self.logger.error("ファイル構造エラー: 最低限のレコード数が不足")
                return False
            
            # 最初の行はファイルヘッダー
            first_line = lines[0].rstrip('\n\r')
            if not (first_line.startswith("NNN0")):
                self.logger.error("ファイル構造エラー: ファイルヘッダーが正しくありません")
                return False
            
            # 最後の行にトレーラーが含まれているかチェック
            last_line = lines[-1].rstrip('\n\r')
            # 256バイト行の前半128バイト（0-127）または後半128バイト（128-255）にNNN9があるかチェック
            has_trailer = (last_line.startswith("NNN9") or 
                          (len(last_line) >= 132 and last_line[128:132] == "NNN9"))
            
            if not has_trailer:
                self.logger.error("ファイル構造エラー: トレーラーが正しくありません")
                return False
            
            self.logger.info("ファイル構造検証完了")
            return True
            
        except Exception as e:
            self.logger.error(f"ファイル構造検証エラー: {e}")
            return False 