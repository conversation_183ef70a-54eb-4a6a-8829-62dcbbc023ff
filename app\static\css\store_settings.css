/* store_settings.html 専用のスタイル */

/* ページ全体のコンテナ */
.store-settings-page {
    max-width: 1200px;
    margin: 0 auto;
}

/* 新規登録フォームの調整 */
.store-settings-page input[name="store_code"] {
    max-width: 100px;
}

.store-settings-page input[name="store_name"] {
    max-width: 400px;
}

.store-settings-page input[name="store_name_kana"] {
    max-width: 400px;
}

/* テーブルのカラム幅調整 */
.store-settings-page .table th:nth-child(1),
.store-settings-page .table td:nth-child(1) {
    width: 100px;
    text-align: center;
}

.store-settings-page .table th:nth-child(2),
.store-settings-page .table td:nth-child(2) {
    width: auto;
}

.store-settings-page .table th:nth-child(3),
.store-settings-page .table td:nth-child(3) {
    width: auto;
}

.store-settings-page .table th:nth-child(4),
.store-settings-page .table td:nth-child(4) {
    width: 280px;
    text-align: center;
}

/* 編集モードの入力フィールド */
.store-settings-page .edit-name {
    max-width: 100%;
}

.store-settings-page .edit-name-kana {
    max-width: 100%;
}

/* ボタングループの調整 */
.store-settings-page .btn-sm {
    margin-left: 5px;
}

/* レスポンシブ対応 */
@media (max-width: 768px) {
    .store-settings-page .table td:nth-child(4) {
        text-align: left;
    }
    
    .store-settings-page .btn-sm {
        margin: 2px;
    }
} 