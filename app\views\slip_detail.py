from flask import Blueprint, jsonify, request, current_app, session, render_template
from flask_login import login_required, current_user
from app.models import (
    PurchaseSlip, PurchaseSlipDetail, Store, Section, 
    Item, PurchaseSlipHistory, Vendor
)
from app import db
from datetime import datetime, date
from decimal import Decimal, InvalidOperation
import json

slip_detail = Blueprint('slip_detail', __name__)

##承認前バリデーション
@slip_detail.route('/<int:slip_id>/validate', methods=['POST'])
@login_required
def validate_slip(slip_id):
    try:
        slip = PurchaseSlip.query.get_or_404(slip_id)
        
        # バリデーションチェック
        validation_errors = validate_slip_for_approval(slip)
        
        # 課コードの不一致チェック（section_id = NULLの場合も含む）
        mismatched_items = []
        for detail in slip.details:
            # section_idがNULLの場合は不一致として扱う
            if detail.section_id is None:
                mismatched_items.append({
                    'product_code': detail.product_code,
                    'department_code': 'NULL'
                })
            # item_idが存在し、かつitemが取得できた場合は課コードをチェック
            elif detail.item_id and detail.item:
                item = detail.item
                if item and item.department:
                    section = item.department  # すでにdepartmentリレーションで取得可能
                    if section and section.section_code != slip.department_code:
                        mismatched_items.append({
                            'product_code': detail.product_code,
                            'department_code': section.section_code
                        })
        
        # バリデーションエラーがある場合は、課コードの不一致に関係なくエラーを優先
        if validation_errors:
            return jsonify({
                'error': '入力内容に問題があります',
                'validation_errors': validation_errors,
                'error_details': '\n'.join(validation_errors)
            }), 400
        
        # バリデーションエラーがない場合のみ、課コードの不一致を警告として表示
        if mismatched_items:
            return jsonify({
                'status': 'warning',
                'message': 'ヘッダの課コードと異なる課に所属する商品がありますが承認しますか？\n承認した場合、伝票の課の代表商品で連携されます。',
                'mismatched_items': mismatched_items
            }), 409  # 409 Conflictを使用して警告状態を示す
            
        return jsonify({'message': 'バリデーションチェックOK'})

    except Exception as e:
        current_app.logger.error(f"バリデーションチェック中にエラーが発生: {str(e)}")
        return jsonify({'error': 'バリデーションチェック中にエラーが発生しました'}), 500

def validate_slip_for_approval(slip):
    """仕入伝票の承認前バリデーション"""
    errors = []
    today = date.today()

    try:
        # 基本チェック：Noneチェック
        if slip is None:
            return ['伝票が見つかりません']

        # ヘッダー必須項目のチェック（属性の存在確認を含む）
        required_fields = {
            '伝票番号': getattr(slip, 'slip_number', None),
            '会社コード': getattr(slip, 'company_code', None),
            '取引先コード': getattr(slip, 'vendor_code', None),
            '取引先名': getattr(slip, 'vendor_name', None),
            '日付': getattr(slip, 'slip_date', None),
            '店舗コード': getattr(slip, 'store_code', None),
            '店舗名': getattr(slip, 'store_name', None),
            '課コード': getattr(slip, 'department_code', None),
            '税区分': getattr(slip, 'tax_division_code', None)
        }
        
        # 必須項目の存在チェック
        for field_name, value in required_fields.items():
            # デバッグ出力を追加
            current_app.logger.info(f"バリデーション中: {field_name} = {value}")
            
            # 値が None または空文字列の場合
            if value is None or (isinstance(value, str) and not value.strip()):
                errors.append(f'{field_name}は必須項目です')
                continue

            # 文字列に変換して空白を除去
            if isinstance(value, str):
                value = value.strip()

            # 各項目の個別バリデーション
            if field_name == '伝票番号' and value:
                if not value.isdigit():
                    errors.append('伝票番号は半角数字で入力してください')
                elif len(value) > 10:
                    errors.append('伝票番号は10桁以下で入力してください')
                elif value.startswith('0'):
                    errors.append('伝票番号は0以外から始めてください')
                elif not '1' <= value <= '9999999999':
                    errors.append('伝票番号は1から9999999999の範囲で入力してください')

            elif field_name == '会社コード' and value:
                valid_company_codes = ['01', '02', '03', '04', '05', '06']
                if value not in valid_company_codes:
                    errors.append('会社コードは01、02、03、04、05、06のいずれかを入力してください')

            elif field_name == '取引先コード' and value:
                if not value.isdigit():
                    errors.append('取引先コードは半角数字で入力してください')
                elif len(value) != 4:
                    errors.append('取引先コードは4桁で入力してください')
                else:
                    vendor = Vendor.query.filter_by(vendor_code=value).first()
                    if not vendor:
                        errors.append('指定された取引先コードはマスタに登録されていません')

            elif field_name == '店舗コード' and value:
                if not value.isdigit():
                    errors.append('店舗コードは半角数字で入力してください')
                elif len(value) != 2:
                    errors.append('店舗コードは2桁で入力してください')
                else:
                    store = Store.query.filter_by(store_code=value).first()
                    if not store:
                        errors.append('指定された店舗コードはマスタに登録されていません')

            elif field_name == '課コード':
                department_code = value.strip()
                current_app.logger.info(f"課コードのバリデーション: {department_code}")

                if not department_code.isdigit():
                    errors.append('課コードは半角数字で入力してください')
                    continue
                    
                if len(department_code) != 3:
                    errors.append('課コードは3桁で入力してください')
                    continue
                
                # マスタ存在チェック
                section = Section.query.filter_by(
                    section_code=department_code,
                    code_level=1,
                    del_flg=0
                ).first()
                
                current_app.logger.info(f"課コードのマスタ検索結果: {section}")
                
                if section is None:
                    current_app.logger.warning(f"課コード {department_code} はマスタに存在しません")
                    errors.append('指定された課コードはマスタに登録されていません')
                    continue

            elif field_name == '税区分' and value:
                valid_tax_codes = ['01', '02', '03', '04', '05']
                if value not in valid_tax_codes:
                    errors.append('税区分は01、02、03、04、05のいずれかを入力してください')

        # 日付のバリデーション
        if getattr(slip, 'slip_date', None):
            days_diff = (slip.slip_date - today).days
            if days_diff < -60:
                errors.append('伝票日付は60日前までの日付を入力してください')
            elif days_diff > 30:
                errors.append('伝票日付は30日後までの日付を入力してください')

        # 明細のチェック
        if not slip.details:
            errors.append('明細が登録されていません')
        else:
            for detail in slip.details:
                # 商品コードチェック（既存）
                if not detail.product_code:
                    errors.append(f'明細行{detail.line_number}: 商品コードは必須です')

                # 数量チェック（修正）
                try:
                    quantity = float(detail.quantity)
                    if quantity < 1.00 or quantity > 999999:
                        errors.append(f'明細行{detail.line_number}: 数量は1.00～999999の範囲で入力してください')
                except (TypeError, ValueError):
                    errors.append(f'明細行{detail.line_number}: 数量は数値で入力してください')

                # 原単価チェック（修正）
                try:
                    unit_cost = float(detail.unit_cost)
                    if unit_cost < 1.00 or unit_cost > 999999.99:
                        errors.append(f'明細行{detail.line_number}: 原単価は1.00～999999.99の範囲で入力してください')
                except (TypeError, ValueError):
                    errors.append(f'明細行{detail.line_number}: 原単価は数値で入力してください')

                # 売単価チェック（修正）
                try:
                    unit_price = float(detail.unit_price)
                    if unit_price < 0 or unit_price > 9999999:
                        errors.append(f'明細行{detail.line_number}: 売単価は0～9999999の範囲で入力してください')
                except (TypeError, ValueError):
                    errors.append(f'明細行{detail.line_number}: 売単価は数値で入力してください')

        # 明細行の合計金額チェックを追加
        if slip.details:
            # 明細行の原価金額合計を計算
            detail_cost_total = sum(detail.quantity * detail.unit_cost for detail in slip.details)
            # 明細行の売価金額合計を計算
            detail_selling_total = sum(detail.quantity * detail.unit_price for detail in slip.details)

            # 原価金額の整合性チェック
            if abs(detail_cost_total - slip.cost_amount) > Decimal('0.01'):  # 小数点以下の誤差を許容
                errors.append(
                    f'原価金額の合計が一致しません。'
                    f'明細合計: {detail_cost_total:.2f}, '
                    f'伝票合計: {slip.cost_amount:.2f}'
                )

            # 売価金額の整合性チェック
            if abs(detail_selling_total - slip.selling_amount) > Decimal('0.01'):  # 小数点以下の誤差を許容
                errors.append(
                    f'売価金額の合計が一致しません。'
                    f'明細合計: {detail_selling_total:.2f}, '
                    f'伝票合計: {slip.selling_amount:.2f}'
                )

        # 課コードの不一致チェックは警告として別途処理するため、ここでは削除

        # バッグ出力を追加
        current_app.logger.info(f"バリデーション結果のエラー: {errors}")
        return errors

    except Exception as e:
        current_app.logger.error(f"バリデーション処理中にエラーが発生: {str(e)}")
        return [f'バリデーション処理中にエラーが発生しました: {str(e)}']

def validate_department_code(department_code):
    """課コードのバリデーション"""
    errors = []
    
    if not department_code:
        return ['課コードを入力してください']
        
    if not department_code.isdigit():
        errors.append('課コードは半角数字で入力してください')
    elif len(department_code) != 3:
        errors.append('課コードは3桁で入力してください')
    else:
        try:  # try-exceptブロックを追加
            section = Section.query.filter_by(
                section_code=department_code,
                code_level=1,
                del_flg=0
            ).first()
            if not section:
                errors.append('指定された課コードはマスタに登録されていません')
        except Exception as e:
            current_app.logger.error(f"課コードの検証中にエラー: {str(e)}")
            errors.append('課コードの検証中にエラーが発生しました')
            
    return errors

##承認
@slip_detail.route('/<int:slip_id>/approve', methods=['POST'])
@login_required
def approve_slip(slip_id):
    if not current_user.is_approver:
        return jsonify({'error': '承認権限がありません'}), 403

    try:
        slip = PurchaseSlip.query.get_or_404(slip_id)
        
        if slip.status == 'approved':
            return jsonify({'error': 'この伝票は既に承認済みです'}), 400

        # 承認前のバリデーションチェック
        validation_errors = validate_slip_for_approval(slip)
        if validation_errors:
            return jsonify({
                'error': '入力内容に問題があります',
                'validation_errors': validation_errors,
                'error_details': '\n'.join(validation_errors)
            }), 400

        # 課コードの不一致チェック（section_id = NULLの場合も含む）
        for detail in slip.details:
            needs_update = False
            
            # section_idがNULLの場合は不一致として扱う
            if detail.section_id is None:
                needs_update = True
            # item_idがあり、かつitemが取得できる場合は課コードをチェック
            elif detail.item_id and detail.item:
                item = detail.item
                if item and item.department:
                    section = item.department
                    if section and section.section_code != slip.department_code:
                        needs_update = True
            
            # 更新が必要な場合は代表商品で更新
            if needs_update:
                # 伝票の課コードで代表商品を検索（item_type = 3の条件を追加）
                representative_item = Item.query.join(Section, Item.dept_id == Section.id)\
                    .filter(Section.section_code == slip.department_code)\
                    .filter(Item.del_flg == 0)\
                    .filter(Item.item_type == 3)\
                    .order_by(Item.item_code)\
                    .first()
                
                if representative_item:
                    # 代表商品で更新（section_idも更新）
                    detail.item_id = representative_item.id
                    detail.product_code = representative_item.item_code
                    detail.product_name = representative_item.item_name
                    detail.section_id = representative_item.section_id
                else:
                    return jsonify({
                        'error': f'課コード {slip.department_code} の代表商品が見つかりません'
                    }), 400

        # 承認処理
        slip.status = 'approved'
        slip.approved_at = datetime.now()
        slip.approved_by = current_user.id

        # 履歴を記録
        history = PurchaseSlipHistory(
            slip_id=slip.id,
            action='approve',
            user_id=current_user.id,
            details={'changes': '伝票を承認しました'}
        )
        db.session.add(history)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '伝票を承認しました。課コードが不一致の明細は伝票の課の代表商品で更新されました。'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"伝票承認中にエラーが発生しました: {str(e)}")
        return jsonify({'error': '伝票承認中にエラーが発生しました'}), 500

##承認取消
@slip_detail.route('/<int:slip_id>/unapprove', methods=['POST'])
@login_required
def unapprove_slip(slip_id):
    if not current_user.is_approver:
        return jsonify({'error': '承認取消権限がありません'}), 403

    try:
        slip = PurchaseSlip.query.get_or_404(slip_id)
        
        # 承認状態チェック
        if slip.status != 'approved':
            return jsonify({'error': 'この伝票は承認されていません'}), 400

        # 送信済みチェック
        if slip.send_flg:
            return jsonify({'error': '送信済みの伝票は承認取消できません'}), 400

        # 承認取消処理
        slip.status = 'pending'
        slip.approved_at = None
        slip.approved_by = None
        slip.updated_at = datetime.now()

        # 履歴を記録
        history = PurchaseSlipHistory(
            slip_id=slip.id,
            action='unapprove',
            user_id=current_user.id,
            details={'changes': '伝票の承認を取消しました'}
        )
        db.session.add(history)
        db.session.commit()

        return jsonify({'status': 'success', 'message': '伝票の承認を取消しました。'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"承認取消中にエラーが発生しました: {str(e)}")
        return jsonify({'error': '承認取消中にエラーが発生しました'}), 500

##削除
@slip_detail.route('/<int:slip_id>/delete', methods=['POST'])
@login_required
def delete_slip(slip_id):
    try:
        slip = PurchaseSlip.query.get_or_404(slip_id)
        
        if slip.status == 'approved':
            return jsonify({'error': '承認済みの伝票は削除できません。'}), 400

        # 履歴を記録
        history = PurchaseSlipHistory(
            slip_id=slip.id,
            action='delete',
            user_id=current_user.id,
            details={'changes': '伝票を削除しました'}
        )
        db.session.add(history)
        
        # 伝票を削除
        slip.is_deleted = True
        db.session.commit()

        return jsonify({'status': 'success', 'message': '伝票を削除しました。'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"伝票削除中にエラーが発生しました: {str(e)}")
        return jsonify({'error': '伝票削除中にエラーが発生しました'}), 500

##ヘッダー情報取得
@login_required
def get_slip_header(slip_id):
    try:
        slip = PurchaseSlip.query.get_or_404(slip_id)
        return jsonify({
            'slip_number': slip.slip_number,
            'company_code': slip.company_code,
            'vendor_code': slip.vendor_code,
            'vendor_name': slip.vendor_name,
            'department_code': slip.department_code,
            'store_code': slip.store_code,
            'store_name': slip.store_name,
            'tax_division_code': slip.tax_division_code,
            'status': 'success'
        })
    except Exception as e:
        current_app.logger.error(f"伝票ヘッダー情報取得中にエラーが発生: {str(e)}")
        return jsonify({
            'error': '伝票情報の取得中にエラーが発生しました',
            'status': 'error'
        }), 500

##伝票番号更新
@slip_detail.route('/<int:slip_id>/number/update', methods=['PUT'])
@login_required
def update_slip_number(slip_id):
    try:
        data = request.get_json()
        slip = PurchaseSlip.query.get_or_404(slip_id)

        if 'slip_number' in data:
            slip_number = data['slip_number'].strip()

            # バリデーション
            if not slip_number:
                return jsonify({
                    'error': '伝票番号は必須です',
                    'status': 'error',
                    'slip_number': slip.slip_number
                }), 400

            if not slip_number.isdigit():
                return jsonify({
                    'error': '伝票番号は半角数字のみ入力可能です',
                    'status': 'error',
                    'slip_number': slip.slip_number
                }), 400

            if len(slip_number) > 10:
                return jsonify({
                    'error': '伝票番号は10桁以下で入力してください',
                    'status': 'error',
                    'slip_number': slip.slip_number
                }), 400

            if slip_number.startswith('0'):
                return jsonify({
                    'error': '伝票番号は0以外から始めてください',
                    'status': 'error',
                    'slip_number': slip.slip_number
                }), 400

            # 変更前の値を保存
            old_slip_number = slip.slip_number

            # 値を更新
            slip.slip_number = slip_number

            # 履歴を記録
            history = PurchaseSlipHistory(
                slip_id=slip.id,
                action='edit',
                user_id=current_user.id,
                details={
                    'field': 'slip_number',
                    'old_value': old_slip_number,
                    'new_value': slip_number,
                    'changes': f'伝票番号を{old_slip_number or "未設定"}から{slip_number}に変更'
                }
            )
            db.session.add(history)
            db.session.commit()

            return jsonify({
                'status': 'success',
                'slip_number': slip.slip_number
            })

        return jsonify({
            'error': '伝票番号が不足しています',
            'status': 'error',
            'slip_number': slip.slip_number
        }), 400

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"伝票番号更新エラー: {str(e)}")
        return jsonify({
            'error': '更新に失敗しました',
            'status': 'error',
            'slip_number': slip.slip_number
        }), 500

##会社コード更新
@slip_detail.route('/<int:slip_id>/company/update', methods=['PUT'])
@login_required
def update_company_code(slip_id):
    try:
        slip = PurchaseSlip.query.get_or_404(slip_id)
        
        if slip.status == 'approved':
            return jsonify({'error': '承認済みの伝票は編集できません。'}), 400

        data = request.get_json()
        company_code = data.get('company_code')

        if not company_code:
            return jsonify({
                'error': '会社コードを入力してください',
                'company_code': slip.company_code
            }), 400

        # 会社コードのバリデーション
        valid_company_codes = ['01', '02', '03', '04', '05', '06']
        if company_code not in valid_company_codes:
            return jsonify({
                'error': '会社コードは01、02、03、04、05、06のいずれかを入力してください',
                'company_code': slip.company_code
            }), 400

        # 変更前の値を保存
        old_company_code = slip.company_code

        # 会社コードを更新
        slip.company_code = company_code

        # 履歴を記録
        history = PurchaseSlipHistory(
            slip_id=slip.id,
            action='edit',
            user_id=current_user.id,
            details={
                'changes': f'会社コードを{old_company_code or "未設定"}から{company_code}に変更'
            }
        )
        db.session.add(history)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '会社コードを更新しました',
            'company_code': company_code
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"会社コード更新中にエラーが発生しました: {str(e)}")
        return jsonify({'error': '更新処理中にエラーが発生しました。'}), 500

##取引先コード更新
@slip_detail.route('/<int:slip_id>/vendor/update', methods=['PUT'])
@login_required
def update_vendor_code(slip_id):
    try:
        slip = PurchaseSlip.query.get_or_404(slip_id)
        
        if slip.status == 'approved':
            return jsonify({'error': '承認済みの伝票は編集できません。'}), 400

        data = request.get_json()
        vendor_code = data.get('vendor_code')

        if not vendor_code:
            return jsonify({'error': '取引先コードが必要です'}), 400

        # 取引先コードのバリデーション
        if not vendor_code.isdigit() or len(vendor_code) != 4:
            return jsonify({
                'error': '取引先コードは4桁の数字で入力してください',
                'vendor_code': slip.vendor_code
            }), 400

        # マスタ検索を更新処理内で実行
        vendor = Vendor.query.filter_by(vendor_code=vendor_code).first()
        if not vendor:
            return jsonify({
                'error': '指定された取引先コードは登録されていません',
                'vendor_code': slip.vendor_code
            }), 400

        # 変更前の値を保存
        old_vendor_code = slip.vendor_code

        # 取引先情報を更新
        slip.vendor_code = vendor_code
        slip.vendor_name = vendor.vendor_name
        # Vendorの税区分を伝票に自動設定
        slip.tax_division_code = vendor.tax_type_code
        # Vendorの消費税率を伝票に自動設定
        slip.tax_rate = vendor.tax_rate

        # 履歴を記録
        history = PurchaseSlipHistory(
            slip_id=slip.id,
            action='edit',
            user_id=current_user.id,
            details={
                'changes': f'取引先コードを{old_vendor_code or "未設定"}から{vendor_code}に変更、税区分を{vendor.tax_type_code}、消費税率を{vendor.tax_rate}%に設定'
            }
        )
        db.session.add(history)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '取引先コードを更新しました',
            'vendor_code': vendor_code,
            'vendor_name': vendor.vendor_name,
            'tax_division_code': vendor.tax_type_code,
            'tax_rate': float(vendor.tax_rate)
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"取引先コード更新中にエラーが発生しました: {str(e)}")
        return jsonify({'error': '更新処理中にエラーが発生しました。'}), 500

##伝票日付更新
@slip_detail.route('/<int:slip_id>/date/update', methods=['PUT'])
@login_required
def update_slip_date(slip_id):
    # セッションチェック
    if not session.get('_id'):
        return jsonify({'error': 'セッションが無効です'}), 401

    try:
        data = request.get_json()
        slip = PurchaseSlip.query.get_or_404(slip_id)
        
        if slip.status == 'approved':
            return jsonify({
                'error': '承認済みの伝票は編集できません',
                'status': 'error',
                'slip_date': slip.slip_date.strftime('%Y-%m-%d')
            }), 400

        if 'slip_date' in data:
            new_date = datetime.strptime(data['slip_date'], '%Y-%m-%d').date()
            today = date.today()
            
            # 日付の範囲チェック
            if (today - new_date).days > 60:
                return jsonify({
                    'error': '伝票日付は60日前までの日付を入力してください',
                    'status': 'error',
                    'slip_date': slip.slip_date.strftime('%Y-%m-%d')
                }), 400
            
            if (new_date - today).days > 30:
                return jsonify({
                    'error': '伝票日付は30日後までの日付を入力してください',
                    'status': 'error',
                    'slip_date': slip.slip_date.strftime('%Y-%m-%d')
                }), 400

            # 変更前の値を保存
            old_date = slip.slip_date

            # 値を更新
            slip.slip_date = new_date
            
            # 履歴を記録
            history = PurchaseSlipHistory(
                slip_id=slip.id,
                action='edit',
                user_id=current_user.id,
                details={
                    'field': 'slip_date',
                    'old_value': old_date.strftime('%Y-%m-%d'),
                    'new_value': new_date.strftime('%Y-%m-%d'),
                    'changes': f'日付を{old_date.strftime("%Y/%m/%d")}から{new_date.strftime("%Y/%m/%d")}に変更'
                }
            )
            db.session.add(history)
            db.session.commit()
            
            return jsonify({
                'status': 'success',
                'slip_date': new_date.strftime('%Y-%m-%d')
            })
            
        return jsonify({
            'error': '日付情報が不足しています',
            'status': 'error',
            'slip_date': slip.slip_date.strftime('%Y-%m-%d') if slip.slip_date else None
        }), 400
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"日付更新エラー: {str(e)}")
        return jsonify({
            'error': '更新に失敗しました',
            'status': 'error',
            'slip_date': slip.slip_date.strftime('%Y-%m-%d') if slip.slip_date else None
        }), 500

##課コード更新
@slip_detail.route('/<int:slip_id>/department/update', methods=['PUT'])
@login_required
def update_department_code(slip_id):
    try:
        slip = PurchaseSlip.query.get_or_404(slip_id)
        
        if slip.status == 'approved':
            return jsonify({'error': '承認済みの伝票は編集できません。'}), 400

        data = request.get_json()
        department_code = data.get('department_code')

        # バリデーションチェック
        validation_errors = validate_department_code(department_code)
        if validation_errors:
            return jsonify({
                'error': '課コードを入力してください',
                'department_code': slip.department_code
            }), 400

        # 課コードのバリデーション
        if not department_code.isdigit() or len(department_code) != 3:
            return jsonify({
                'error': '課コードは3桁の数字で入力してください',
                'department_code': slip.department_code
            }), 400

        # 課マスタの検索
        section = Section.query.filter_by(
            section_code=department_code,
            code_level=1,
            del_flg=0
        ).first()

        if not section:
            current_app.logger.warning(f"課コード {department_code} はマスタに存在しません")
            return jsonify({
                'error': '指定された課コードは登録されていません',
                'department_code': slip.department_code
            }), 400

        # 変更前の値を保存
        old_department_code = slip.department_code

        # 課コードを更新
        slip.department_code = department_code

        # 履歴を記録
        history = PurchaseSlipHistory(
            slip_id=slip.id,
            action='edit',
            user_id=current_user.id,
            details={
                'changes': f'課コードを{old_department_code or "未設定"}から{department_code}に変更'
            }
        )
        db.session.add(history)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '課コードを更新しました',
            'department_code': department_code,
            'department_name': section.section_name
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"課コード更新中にエラーが発生しました: {str(e)}")
        return jsonify({'error': '更新処理中にエラーが発生しました。'}), 500

##店舗コード更新
@slip_detail.route('/<int:slip_id>/store/update', methods=['PUT'])
@login_required
def update_store_code(slip_id):
    try:
        slip = PurchaseSlip.query.get_or_404(slip_id)
        
        if slip.status == 'approved':
            return jsonify({'error': '承認済みの伝票は編集できません。'}), 400

        data = request.get_json()
        store_code = data.get('store_code')

        if not store_code:
            return jsonify({
                'error': '店舗コードを入力してください',
                'store_code': slip.store_code
            }), 400

        # 店舗コードのバリデーション
        if not store_code.isdigit() or len(store_code) != 2:
            return jsonify({
                'error': '店舗コードは2桁の数字で入力してください',
                'store_code': slip.store_code
            }), 400

        # 店舗マスタの検索
        store = Store.query.filter_by(store_code=store_code).first()
        if not store:
            return jsonify({
                'error': '指定された店舗コードは登録されていません',
                'store_code': slip.store_code
            }), 400

        # 変更前の値を保存
        old_store_code = slip.store_code

        # 店舗情報を更新
        slip.store_code = store_code
        slip.store_name = store.store_name

        # 履歴を記録
        history = PurchaseSlipHistory(
            slip_id=slip.id,
            action='edit',
            user_id=current_user.id,
            details={
                'changes': f'店舗コードを{old_store_code or "未設定"}から{store_code}に変更'
            }
        )
        db.session.add(history)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '店舗コードを更新しました',
            'store_code': store_code,
            'store_name': store.store_name
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"店舗コード更新中にエラーが発生しました: {str(e)}")
        return jsonify({'error': '更新処理中にエラーが発生しました。'}), 500

##課コードマスタ検索
@slip_detail.route('/section/<department_code>')
@login_required
def get_section(department_code):
    try:
        section = Section.query.filter_by(
            section_code=department_code,
            code_level=1,
            del_flg=0
        ).first()
        
        if section:
            return jsonify({
                'section_code': section.section_code,
                'section_name': section.section_name,
                'status': 'success'
            })
        else:
            current_app.logger.info(f"課コード {department_code} はマスタに登録されていません")
            return jsonify({
                'error': '入力された課コードは、マスタに登録されておりません。',
                'status': 'not_found',
                'section_code': None,
                'section_name': None
            }), 404
            
    except Exception as e:
        current_app.logger.error(f"課コード情報取得中にエラーが発生: {str(e)}")
        return jsonify({
            'error': '課コード情報の取得中にエラーが発生しました',
            'status': 'error',
            'section_code': None,
            'section_name': None
        }), 500
    
##税区分更新
@slip_detail.route('/<int:slip_id>/tax/update', methods=['PUT'])
@login_required
def update_tax_division(slip_id):
    """税区分の直接更新は無効化 - Vendorから自動設定される"""
    return jsonify({
        'error': '税区分は取引先コードから自動設定されます。取引先コードを変更してください。',
        'message': '税区分の直接変更はできません'
    }), 400

##商品コード更新
@slip_detail.route('/<int:slip_id>/details/<int:detail_id>/product', methods=['PUT'])
@login_required
def update_slip_detail_product(slip_id, detail_id):
    try:
        slip = PurchaseSlip.query.get_or_404(slip_id)
        detail = PurchaseSlipDetail.query.get_or_404(detail_id)
        
        if slip.status == 'approved':
            return jsonify({'error': '承認済みの伝票は編集できません。'}), 400

        data = request.get_json()
        product_code = data.get('product_code')
        line_number = data.get('line_number', '')
        old_value = data.get('old_value', '')
        field_label = data.get('field_label', '商品コード')

        # 商品マスタの検索
        item = Item.query.filter_by(item_code=product_code, del_flg=0).first()
        
        # 明細の更新
        detail.product_code = product_code
        detail.item_id = item.id if item else None
        detail.section_id = item.section_id if item else None
        detail.product_name = item.item_name if item else ''

        # 履歴登録
        history = PurchaseSlipHistory(
            slip_id=slip.id,
            action='edit',
            user_id=current_user.id,
            details={
                'detail_id': detail.id,
                'changes': f'{line_number}行目: {field_label}を{old_value or "未設定"}から{product_code}に変更'
            }
        )
        db.session.add(history)
        db.session.commit()

        # レスポンスデータの作成
        response_data = {
            'status': 'success',
            'product_name': item.item_name if item else '',
            'has_item': item is not None,
            'department_code': item.department.section_code if item and item.department else '',
            'section_id': item.section_id if item else None,
            'class_code': item.section.section_code if item and item.section else '',
            'item': {
                'unit_cost': float(item.unit_cost) if item else None,
                'unit_price': int(item.unit_price) if item else None
            } if item else None
        }

        return jsonify(response_data)

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"商品コード更新中にエラーが発生: {str(e)}")
        return jsonify({'error': '更新処理中にエラーが発生しました。'}), 500

@slip_detail.route('/<int:slip_id>', methods=['GET'])
@login_required
def detail(slip_id):
    slip = PurchaseSlip.query.get_or_404(slip_id)
    
    # Vendor情報が設定されているが税区分が空の場合、Vendorから税区分を自動設定
    if slip.vendor_code and not slip.tax_division_code:
        vendor = Vendor.query.filter_by(vendor_code=slip.vendor_code).first()
        if vendor:
            slip.tax_division_code = vendor.tax_type_code
            slip.tax_rate = vendor.tax_rate
            db.session.commit()
    
    next_pending_slip = PurchaseSlip.query.filter(
        PurchaseSlip.status == 'pending',
        PurchaseSlip.id > slip_id
    ).first()
    return render_template('slip_detail.html', slip=slip, next_pending_slip=next_pending_slip)

# 履歴取得API
@slip_detail.route('/<int:slip_id>/history', methods=['GET'])
@login_required
def get_slip_history(slip_id):
    try:
        histories = PurchaseSlipHistory.query\
            .filter_by(slip_id=slip_id)\
            .order_by(PurchaseSlipHistory.created_at.desc())\
            .all()
        
        return jsonify([{
            'created_at': history.created_at.isoformat(),
            'action': history.action,
            'user': {
                'username': history.user.username
            },
            'details': history.details
        } for history in histories])

    except Exception as e:
        current_app.logger.error(f"履歴取得中にエラーが発生: {str(e)}")
        return jsonify({'error': '履歴の取得に失敗しました'}), 500

# 合計金額更新API
@slip_detail.route('/<int:slip_id>/update-totals', methods=['POST'])
@login_required
def update_totals(slip_id):
    try:
        slip = PurchaseSlip.query.get_or_404(slip_id)
        
        # 合計金額を再計算
        cost_total = sum(detail.quantity * detail.unit_cost for detail in slip.details)
        selling_total = sum(detail.quantity * detail.unit_price for detail in slip.details)
        
        # 伝票の合計金額を更新
        slip.cost_amount = cost_total
        slip.selling_amount = selling_total
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'cost_total': float(cost_total),
            'selling_total': float(selling_total)
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"合計金額更新中にエラー: {str(e)}")
        return jsonify({'error': '合計金額の更新に失敗しました'}), 500

# 数量の更新
@slip_detail.route('/<int:slip_id>/details/<int:detail_id>/quantity', methods=['PUT'])
@login_required
def update_slip_detail_quantity(slip_id, detail_id):
    try:
        slip = PurchaseSlip.query.get_or_404(slip_id)
        detail = PurchaseSlipDetail.query.get_or_404(detail_id)
        
        if slip.status == 'approved':
            return jsonify({'error': '承認済みの伝票は編集できません。'}), 400

        data = request.get_json()
        try:
            quantity = Decimal(str(data.get('quantity', 0)))
        except (TypeError, ValueError, InvalidOperation):
            return jsonify({'error': '数量の値が不正です。'}), 400
        
        if quantity < 0:
            return jsonify({'error': '数量は0以上の値を入力してください'}), 400

        # 変更前の数量を保存
        old_quantity = detail.quantity

        # 明細の更新と金額計算
        detail.quantity = quantity
        detail.amount = (quantity * detail.unit_cost).quantize(Decimal('1'), rounding='ROUND_HALF_UP')

        # ヘッダ金額の再計算
        slip.cost_amount = sum((d.quantity * d.unit_cost).quantize(Decimal('1'), rounding='ROUND_HALF_UP') for d in slip.details)
        slip.selling_amount = sum((d.quantity * d.unit_price).quantize(Decimal('1'), rounding='ROUND_HALF_UP') for d in slip.details)

        # 履歴登録
        history = PurchaseSlipHistory(
            slip_id=slip.id,
            action='edit',
            user_id=current_user.id,
            details={
                'detail_id': detail.id,
                'changes': f'数量を{float(old_quantity)}から{float(quantity)}に変更'
            }
        )
        db.session.add(history)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '更新が完了しました',
            'data': {
                'quantity': float(quantity),
                'cost_amount': float(slip.cost_amount),
                'selling_amount': float(slip.selling_amount)
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"数量更新中にエラーが発生しました: {str(e)}")
        return jsonify({'error': '更新処理中にエラーが発生しました。'}), 500

# 原単価の更新
@slip_detail.route('/<int:slip_id>/details/<int:detail_id>/unit-cost', methods=['PUT'])
@login_required
def update_slip_detail_unit_cost(slip_id, detail_id):
    try:
        slip = PurchaseSlip.query.get_or_404(slip_id)
        detail = PurchaseSlipDetail.query.get_or_404(detail_id)
        
        if slip.status == 'approved':
            return jsonify({'error': '承認済みの伝票は編集できません。'}), 400

        data = request.get_json()
        unit_cost = Decimal(str(float(data.get('unit_cost', 0))))
        
        if unit_cost < 1:
            return jsonify({'error': '原単価は1.00以上の値を入力してください'}), 400

        # 変更前の原単価を保存
        old_unit_cost = detail.unit_cost

        # 明細の更新
        detail.unit_cost = unit_cost
        # 原価金額を整数に丸める
        detail.amount = (detail.quantity * unit_cost).quantize(Decimal('1'), rounding='ROUND_HALF_UP')

        # 伝票ヘッダの金額を再計算し、整数に丸める
        slip.cost_amount = sum((d.quantity * d.unit_cost).quantize(Decimal('1'), rounding='ROUND_HALF_UP') for d in slip.details)

        # 履歴登録と保存
        history_details = json.dumps({
            'detail_id': detail.id,
            'changes': f'原単価を{float(old_unit_cost)}から{float(unit_cost)}に変更'
        }, ensure_ascii=False)
        
        history = PurchaseSlipHistory(
            slip_id=slip.id,
            action='edit',
            user_id=current_user.id,
            details=history_details  # JSON文字列として保存
        )
        db.session.add(history)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '更新が完了しました',
            'data': {
                'unit_cost': float(unit_cost),
                'cost_amount': float(slip.cost_amount)
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"原単価更新中にエラーが発生しました: {str(e)}")
        return jsonify({'error': '更新処理中にエラーが発生しました。'}), 500

# 売単価の更新
@slip_detail.route('/<int:slip_id>/details/<int:detail_id>/unit-price', methods=['PUT'])
@login_required
def update_slip_detail_unit_price(slip_id, detail_id):
    try:
        slip = PurchaseSlip.query.get_or_404(slip_id)
        detail = PurchaseSlipDetail.query.get_or_404(detail_id)
        
        if slip.status == 'approved':
            return jsonify({'error': '承認済みの伝票は編集できません。'}), 400

        data = request.get_json()
        unit_price = Decimal(str(float(data.get('unit_price', 0)))).quantize(Decimal('1'), rounding='ROUND_HALF_UP')
        
        if unit_price < 0:
            return jsonify({'error': '売単価は0以上の値を入力してください'}), 400

        # 変更前の売単価を保存
        old_unit_price = detail.unit_price

        # 明細の売単価を更新（整数値）
        detail.unit_price = unit_price

        # 伝票ヘッダの売価金額を再計算（整数値）
        slip.selling_amount = sum(d.quantity * d.unit_price for d in slip.details)

        # 履歴登録と保存
        history_details = json.dumps({
            'detail_id': detail.id,
            'changes': f'売単価を{float(old_unit_price)}から{float(unit_price)}に変更'
        }, ensure_ascii=False)
        
        history = PurchaseSlipHistory(
            slip_id=slip.id,
            action='edit',
            user_id=current_user.id,
            details=history_details  # JSON文字列として保存
        )
        db.session.add(history)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '更新が完了しました',
            'data': {
                'unit_price': int(unit_price),  # 整数値として返す
                'selling_amount': int(slip.selling_amount)
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"売単価更新中にエラーが発生しました: {str(e)}")
        return jsonify({'error': '更新処理中にエラーが発生しました。'}), 500