#!/usr/bin/env python3
"""
データベース管理クラス
"""
import logging
import pyodbc
from contextlib import contextmanager
from datetime import datetime
from decimal import Decimal
from typing import List, Dict, Any, Optional

from models import PurchaseSlip, PurchaseSlipDetail
from utils import ConfigManager


class DatabaseManager:
    """データベース管理クラス"""
    
    def __init__(self, config: ConfigManager):
        """
        Args:
            config: 設定管理インスタンス
        """
        self.config = config
        self.db_config = config.get_database_config()
        self.inventory_db_config = config.get_inventory_database_config()
        self.connection = None
        self.inventory_connection = None
        self.transaction = None
        self.logger = logging.getLogger(__name__)
    
    def connect(self):
        """データベースに接続"""
        try:
            # メインデータベース（YszOCRInvoice）に接続
            connection_string = (
                f"DRIVER={{{self.db_config['driver']}}};"
                f"SERVER={self.db_config['server']};"
                f"DATABASE={self.db_config['database']};"
                f"UID={self.db_config['username']};"
                f"PWD={self.db_config['password']};"
                f"TrustServerCertificate=yes;"
            )
            
            self.connection = pyodbc.connect(connection_string)
            self.connection.autocommit = False  # トランザクション制御のため
            self.logger.info("メインデータベース接続成功")
            
            # 在庫データベース（YszInventory）に接続
            inventory_connection_string = (
                f"DRIVER={{{self.inventory_db_config['driver']}}};"
                f"SERVER={self.inventory_db_config['server']};"
                f"DATABASE={self.inventory_db_config['database']};"
                f"UID={self.inventory_db_config['username']};"
                f"PWD={self.inventory_db_config['password']};"
                f"TrustServerCertificate=yes;"
            )
            
            self.inventory_connection = pyodbc.connect(inventory_connection_string)
            self.inventory_connection.autocommit = True  # 読み取り専用のため
            self.logger.info("在庫データベース接続成功")
            
        except Exception as e:
            self.logger.error(f"データベース接続エラー: {e}")
            raise
    
    def disconnect(self):
        """データベース接続を切断"""
        try:
            if self.transaction:
                self.transaction.rollback()
                self.transaction = None
            
            if self.connection:
                self.connection.close()
                self.connection = None
                self.logger.info("メインデータベース接続切断")
            
            if self.inventory_connection:
                self.inventory_connection.close()
                self.inventory_connection = None
                self.logger.info("在庫データベース接続切断")
                
        except Exception as e:
            self.logger.error(f"データベース切断エラー: {e}")
    
    def close(self):
        """データベース接続を閉じる（disconnectのエイリアス）"""
        self.disconnect()
    
    @contextmanager
    def get_connection(self):
        """コネクションのコンテキストマネージャー"""
        try:
            if not self.connection:
                self.connect()
            yield self.connection
        except Exception as e:
            self.logger.error(f"データベース操作エラー: {e}")
            if self.connection:
                self.connection.rollback()
            raise
        finally:
            # コネクションは維持（明示的にdisconnectで切断）
            pass
    
    def begin_transaction(self):
        """トランザクション開始"""
        try:
            if not self.connection:
                self.connect()
            
            self.transaction = self.connection.cursor()
            self.logger.info("トランザクション開始")
            
        except Exception as e:
            self.logger.error(f"トランザクション開始エラー: {e}")
            raise
    
    def commit_transaction(self):
        """トランザクションコミット"""
        try:
            if self.connection:
                self.connection.commit()
                self.logger.info("トランザクションコミット")
            
            if self.transaction:
                self.transaction.close()
                self.transaction = None
                
        except Exception as e:
            self.logger.error(f"トランザクションコミットエラー: {e}")
            raise
    
    def rollback_transaction(self):
        """トランザクションロールバック"""
        try:
            if self.connection:
                self.connection.rollback()
                self.logger.info("トランザクションロールバック")
            
            if self.transaction:
                self.transaction.close()
                self.transaction = None
                
        except Exception as e:
            self.logger.error(f"トランザクションロールバックエラー: {e}")
    
    def get_target_slips(self) -> List[PurchaseSlip]:
        """送信対象の仕入伝票を取得
        
        Returns:
            仕入伝票リスト
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 仕入伝票の基本情報を取得（storesテーブルとJOINして店舗名カナを取得）
                slip_query = """
                SELECT 
                    ps.id, ps.slip_number, ps.slip_date, ps.company_code, ps.vendor_code, ps.vendor_name,
                    ps.store_code, ps.store_name, ISNULL(s.store_name_kana, ps.store_name) as store_name_kana,
                    ps.department_code, ps.cost_amount, ps.selling_amount,
                    ps.tax_division_code, ps.tax_rate, ps.status, ps.created_at, ps.updated_at,
                    ps.send_flg, ps.approved_at, ps.approved_by, ps.err_description, ps.is_deleted, ps.send_datetime
                FROM dbo.purchase_slip ps
                LEFT JOIN dbo.stores s ON ps.store_code = s.store_code
                WHERE ps.is_deleted = 0
                  AND ps.send_flg = 0
                  AND ps.approved_by IS NOT NULL
                ORDER BY ps.id
                """
                
                cursor.execute(slip_query)
                slip_rows = cursor.fetchall()
                
                slips = []
                for row in slip_rows:
                    slip = PurchaseSlip(
                        id=row.id,
                        slip_number=row.slip_number,
                        slip_date=row.slip_date,
                        company_code=row.company_code,
                        vendor_code=row.vendor_code,
                        vendor_name=row.vendor_name,
                        store_code=row.store_code,
                        store_name=row.store_name,
                        store_name_kana=row.store_name_kana,
                        department_code=row.department_code,
                        cost_amount=row.cost_amount,
                        selling_amount=row.selling_amount,
                        tax_division_code=row.tax_division_code,
                        tax_rate=row.tax_rate,
                        status=row.status,
                        created_at=row.created_at,
                        updated_at=row.updated_at,
                        send_flg=row.send_flg,
                        approved_at=row.approved_at,
                        approved_by=row.approved_by,
                        err_description=row.err_description,
                        is_deleted=row.is_deleted,
                        send_datetime=row.send_datetime,
                        details=[]
                    )
                    
                    # 明細データを取得
                    detail_query = """
                    SELECT 
                        id, slip_id, line_number, product_code, product_name, unit,
                        quantity, unit_cost, unit_price, amount, item_id, section_id,
                        org_product_code, org_product_name, org_unit, org_quantity,
                        org_unit_cost, org_unit_price, org_amount
                    FROM dbo.purchase_slip_detail
                    WHERE slip_id = ?
                    ORDER BY line_number
                    """
                    
                    cursor.execute(detail_query, (slip.id,))
                    detail_rows = cursor.fetchall()
                    
                    for detail_row in detail_rows:
                        detail = PurchaseSlipDetail(
                            id=detail_row.id,
                            slip_id=detail_row.slip_id,
                            line_number=detail_row.line_number,
                            product_code=detail_row.product_code,
                            product_name=detail_row.product_name,
                            unit=detail_row.unit,
                            quantity=detail_row.quantity,
                            unit_cost=detail_row.unit_cost,
                            unit_price=detail_row.unit_price,
                            amount=detail_row.amount,
                            item_id=detail_row.item_id,
                            section_id=detail_row.section_id,
                            org_product_code=detail_row.org_product_code,
                            org_product_name=detail_row.org_product_name,
                            org_unit=detail_row.org_unit,
                            org_quantity=detail_row.org_quantity,
                            org_unit_cost=detail_row.org_unit_cost,
                            org_unit_price=detail_row.org_unit_price,
                            org_amount=detail_row.org_amount
                        )
                        slip.details.append(detail)
                    
                    slips.append(slip)
                
                self.logger.info(f"送信対象伝票取得完了: {len(slips)}件")
                return slips
                
        except Exception as e:
            self.logger.error(f"送信対象伝票取得エラー: {e}")
            raise
    
    def update_send_status(self, slip_ids: List[int]) -> bool:
        """送信ステータスを更新
        
        Args:
            slip_ids: 更新対象の伝票IDリスト
        
        Returns:
            更新成功フラグ
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 送信フラグと送信日時を更新
                update_query = """
                UPDATE dbo.purchase_slip 
                SET send_flg = 1, send_datetime = GETDATE()
                WHERE id = ?
                """
                
                for slip_id in slip_ids:
                    cursor.execute(update_query, (slip_id,))
                
                affected_rows = cursor.rowcount
                self.logger.info(f"送信ステータス更新完了: {affected_rows}件")
                
                return True
                
        except Exception as e:
            self.logger.error(f"送信ステータス更新エラー: {e}")
            return False
    
    def get_slip_count_by_status(self) -> Dict[str, int]:
        """ステータス別伝票件数を取得
        
        Returns:
            ステータス別件数辞書
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                query = """
                SELECT 
                    CASE 
                        WHEN is_deleted = 1 THEN 'deleted'
                        WHEN send_flg = 1 THEN 'sent'
                        WHEN approved_by IS NULL THEN 'pending'
                        ELSE 'approved'
                    END as status,
                    COUNT(*) as count
                FROM dbo.purchase_slip
                GROUP BY 
                    CASE 
                        WHEN is_deleted = 1 THEN 'deleted'
                        WHEN send_flg = 1 THEN 'sent'
                        WHEN approved_by IS NULL THEN 'pending'
                        ELSE 'approved'
                    END
                """
                
                cursor.execute(query)
                rows = cursor.fetchall()
                
                result = {}
                for row in rows:
                    result[row.status] = row.count
                
                return result
                
        except Exception as e:
            self.logger.error(f"ステータス別件数取得エラー: {e}")
            return {}
    
    def get_section_code(self, section_id: int) -> Optional[str]:
        """SectionテーブルからSectionCodeを取得
        
        Args:
            section_id: セクションID
        
        Returns:
            セクションコード（4桁）、見つからない場合はNone
        """
        try:
            if section_id is None:
                return None
            
            if not self.inventory_connection:
                self.logger.error("在庫データベース接続が確立されていません")
                return None
                
            cursor = self.inventory_connection.cursor()
            
            query = """
            SELECT SectionCode
            FROM dbo.Sections
            WHERE Id = ? AND DelFlg = 0
            """
            
            cursor.execute(query, (section_id,))
            row = cursor.fetchone()
            
            if row:
                return row.SectionCode
            else:
                self.logger.warning(f"セクションが見つかりません: section_id={section_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"セクションコード取得エラー: {e}")
            return None

    def validate_data_integrity(self, slip_ids: List[int]) -> bool:
        """データ整合性チェック
        
        Args:
            slip_ids: チェック対象の伝票IDリスト
        
        Returns:
            整合性チェック結果
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                for slip_id in slip_ids:
                    # 伝票の存在チェック
                    slip_query = """
                    SELECT COUNT(*) as count
                    FROM dbo.purchase_slip
                    WHERE id = ? AND is_deleted = 0
                    """
                    cursor.execute(slip_query, (slip_id,))
                    slip_count = cursor.fetchone().count
                    
                    if slip_count == 0:
                        self.logger.error(f"伝票が見つかりません: ID={slip_id}")
                        return False
                    
                    # 明細の存在チェック
                    detail_query = """
                    SELECT COUNT(*) as count
                    FROM dbo.purchase_slip_detail
                    WHERE slip_id = ?
                    """
                    cursor.execute(detail_query, (slip_id,))
                    detail_count = cursor.fetchone().count
                    
                    if detail_count == 0:
                        self.logger.error(f"明細が見つかりません: 伝票ID={slip_id}")
                        return False
                
                self.logger.info(f"データ整合性チェック完了: {len(slip_ids)}件")
                return True
                
        except Exception as e:
            self.logger.error(f"データ整合性チェックエラー: {e}")
            return False 