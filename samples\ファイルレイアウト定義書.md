
# ファイルレイアウト定義書

---

## 🔷 ファイルヘッダ

| 項目名              | 型       | 桁数 | ofs | 寄せ | 内容分類         | セット内容 / 備考 |
|---------------------|----------|------|-----|------|------------------|-------------------|
| データ種別          | 文字列   | 2    | 0   | L    | 固定値           | `"NN"`            |
| レコード種別        | 文字列   | 2    | 2   | L    | 固定値           | `"N0"`            |
| FILLER1             | 文字列   | 20   | 4   | L    | 固定値           | 空白埋め（20桁）  |
| ファイル作成日      | 文字列   | 8    | 24  | L    | ルール生成       | システム日付 `"YYYYMMDD"` |
| ファイル作成時分秒  | 文字列   | 6    | 32  | L    | ルール生成       | システム時刻 `"HHMISS"` |
| データ送信先        | 文字列   | 7    | 38  | L    | データ参照       | 取引先コード（0埋） |
| データ送信元        | 文字列   | 6    | 45  | L    | 固定値           | `"000001"`        |
| レコード長          | 整数     | 3    | 51  | R    | 固定値           | `128`             |
| FILLER2             | 文字列   | 69   | 54  | L    | 固定値           | 空白埋め（69桁）  |
| データシリアルNo    | 整数     | 5    | 123 | R    | 連番管理         | `VAR(KEEP3) + 1`  |

---

## 🔷 伝票ヘッダ

| 項目名              | 型       | 桁数 | ofs | 寄せ | 内容分類         | セット内容 / 備考 |
|---------------------|----------|------|-----|------|------------------|-------------------|
| データ種別          | 文字列   | 2    | 0   | L    | 固定値           | `"NN"`            |
| レコード種別        | 文字列   | 2    | 2   | L    | 固定値           | `"N1"`            |
| 小売店コード        | 文字列   | 7    | 4   | L    | データ参照       | 発注者コード + 納品先コード（0埋） |
| 企業名              | 文字列   | 20   | 11  | L    | データ参照       | 発注者名称カナ    |
| 店舗名              | 文字列   | 20   | 31  | L    | データ参照       | 納品先名称カナ    |
| 分類コード          | 文字列   | 4    | 51  | L    | データ参照       | 中分類コード（0埋）|
| 伝票番号            | 文字列   | 9    | 55  | L    | データ参照・連番 | 取引番号（0埋）   |
| 仕入先コード        | 文字列   | 7    | 64  | L    | データ参照       | 請求取引先コード（0埋） |
| 社店コード          | 文字列   | 12   | 71  | L    | データ参照       | 発注者 + 納品先（0埋） |
| 伝票区分            | 文字列   | 2    | 83  | L    | データ参照       | 処理種別           |
| 取引先コード        | 文字列   | 8    | 85  | L    | データ参照・連番 | 取引先コード（0埋） |
| 便区分              | 文字列   | 3    | 93  | L    | 条件分岐         | 便NO判定 + 補正処理 |
| FILLER              | 文字列   | 13   | 96  | L    | 固定値           | 空白埋め（13桁）  |
| 出力日付区分        | 文字列   | 1    | 109 | L    | 固定値           | `" "`             |
| 発注日              | 日付     | 6    | 110 | L    | データ参照       | 発注日             |
| 納品予定日          | 日付     | 6    | 116 | L    | データ参照       | 納品予定日         |
| 発注区分            | 文字列   | 1    | 122 | L    | 条件分岐         | EOS区分により判定  |
| データシリアルNo    | 整数     | 5    | 123 | R    | 連番管理         | `VAR(KEEP3) + 1`  |

---

## 🔷 明細

（省略、後続で補完可能）

---

## 🔷 トレーラ

| 項目名              | 型       | 桁数 | ofs | 寄せ | 内容分類         | セット内容 / 備考 |
|---------------------|----------|------|-----|------|------------------|-------------------|
| データ種別          | 文字列   | 2    | 0   | L    | 固定値           | `"NN"`            |
| レコード種別        | 文字列   | 2    | 2   | L    | 固定値           | `"N9"`            |
| FILLER1             | 文字列   | 20   | 4   | L    | 固定値           | `"999999..."`     |
| 配信レコード件数    | 整数     | 6    | 24  | R    | カウント         | `VAR(KEEP4) + 1`  |
| 配信アイテム件数    | 整数     | 6    | 30  | R    | カウント         | `VAR(KEEP5)`      |
| FILLER2             | 文字列   | 87   | 36  | L    | 固定値           | 空白埋め           |
| データシリアルNo    | 整数     | 5    | 123 | R    | 連番管理         | `VAR(KEEP3) + 1`  |
