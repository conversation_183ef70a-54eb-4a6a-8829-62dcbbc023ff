import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from utils import ConfigManager

def main():
    try:
        config = ConfigManager()
        db = DatabaseManager(config)
        
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('UPDATE purchase_slip SET send_flg = 0, send_datetime = NULL WHERE send_flg = 1')
            affected_rows = cursor.rowcount
            conn.commit()
            print(f'送信フラグをリセットしました: {affected_rows}件')
            
    except Exception as e:
        print(f'エラー: {e}')

if __name__ == '__main__':
    main() 