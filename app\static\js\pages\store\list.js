// CSRFトークンの取得
function getCsrfToken() {
    return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
}

// 店舗一覧の機能
function initializeStoreList() {
    // 削除ボタンの処理
    document.querySelectorAll('.delete-btn').forEach(button => {
        button.addEventListener('click', handleStoreDelete);
    });
}

// 店舗削除の処理
async function handleStoreDelete() {
    const storeId = this.dataset.storeId;

    if (!confirm('この店舗を削除してもよろしいですか？')) {
        return;
    }

    try {
        const token = document.querySelector('input[name="csrf_token"]').value;
        const response = await fetch(`/store/${storeId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': token
            }
        });

        const data = await response.json();
        if (response.ok) {
            alert(data.message);
            window.location.reload();
        } else {
            let errorMessage = data.error || '店舗の削除に失敗しました';
            
            if (data.details && data.details.related_slips) {
                errorMessage += '\n\n関連する仕入伝票（最新5件）:';
                data.details.related_slips.forEach(slip => {
                    errorMessage += `\n・伝票番号: ${slip.slip_number} (${slip.slip_date}) - ${slip.status}`;
                });
                
                const remainingSlips = data.details.total_slips - data.details.showing_slips;
                if (remainingSlips > 0) {
                    errorMessage += `\n\n他 ${remainingSlips} 件`;
                }
                
                errorMessage += `\n\n合計 ${data.details.total_slips} 件の関連伝票があります。`;
            }
            
            alert(errorMessage);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('エラーが発生しました。詳細はコンソールを確認してください。');
    }
}

export { initializeStoreList }; 