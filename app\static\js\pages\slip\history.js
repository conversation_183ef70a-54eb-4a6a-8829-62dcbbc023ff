import { formatNumber } from '../../common/utils.js';

// 履歴一覧の更新
export async function updateHistoryList(slipId) {
    try {
        const response = await fetch(`/slip/${slipId}/history`);
        const historyData = await response.json();

        if (response.ok) {
            const historyTableBody = document.querySelector('#historyTable tbody');
            if (!historyTableBody) return;

            historyTableBody.innerHTML = ''; // 既存の履歴をクリア

            historyData.forEach(history => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${new Date(history.created_at).toLocaleString('ja-JP')}</td>
                    <td>
                        <span class="badge bg-${getBadgeColor(history.action)}">
                            ${getActionLabel(history.action)}
                        </span>
                    </td>
                    <td>${history.user.username}</td>
                    <td>${formatHistoryDetails(history)}</td>
                `;
                historyTableBody.appendChild(row);
            });
        } else {
            console.error('履歴の取得に失敗しました:', historyData.error);
        }
    } catch (error) {
        console.error('履歴の取得中にエラーが発生しました:', error);
    }
}

// アクションに応じたバッジの色を返す
function getBadgeColor(action) {
    switch (action) {
        case 'approve': return 'success';
        case 'unapprove': return 'warning';
        case 'delete': return 'danger';
        default: return 'info';
    }
}

// アクションのラベルを返す
function getActionLabel(action) {
    switch (action) {
        case 'approve': return '承認';
        case 'unapprove': return '承認取消';
        case 'delete': return '削除';
        default: return '編集';
    }
}

// 履歴の詳細情報をフォーマット
function formatHistoryDetails(history) {
    if (typeof history.details === 'string') {
        try {
            history.details = JSON.parse(history.details);
        } catch (e) {
            return history.details;
        }
    }

    if (history.action === 'approve') {
        return `<span class="text-success">${history.details.changes || '承認'}</span>`;
    } else if (history.action === 'unapprove') {
        // 承認取消の場合もchangesを表示
        return `<span class="text-warning">${history.details.changes || '承認取消'}</span>`;
    } else if (history.action === 'delete') {
        return '<span class="text-danger">伝票削除</span>';
    } else {
        return `<span class="text-info">${history.details.changes || ''}</span>`;
    }
}

// 履歴機能の初期化
export function initializeHistory() {
    const slipDetailForm = document.querySelector('.slip-detail-form');
    if (!slipDetailForm) return;

    const slipId = slipDetailForm.dataset.slipId;
    if (!slipId) return;

    // 初期表示時に履歴を読み込む
    updateHistoryList(slipId);
}
