from flask import Flask, redirect, url_for
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>
from flask_wtf.csrf import CSRFProtect
from config import Config
import os
import logging
import logging.handlers

# 初期化
db = SQLAlchemy()
login_manager = LoginManager()
csrf = CSRFProtect()

@login_manager.user_loader
def load_user(user_id):
    from app.models import User
    return User.query.get(int(user_id))

def create_app():
    app = Flask(__name__,
                template_folder='templates',
                static_folder='static')

    app.config.from_object(Config)

    # セッション設定を明示的に追加
    app.config['SESSION_TYPE'] = 'filesystem'
    app.config['PERMANENT_SESSION_LIFETIME'] = 1800  # 30分
    app.config['SESSION_COOKIE_SECURE'] = False  # 開発環境ではFalse
    app.config['SESSION_COOKIE_HTTPONLY'] = True
    app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'

    # シークレットキーが設定されていることを確認
    if not app.config.get('SECRET_KEY'):
        app.config['SECRET_KEY'] = os.urandom(24)

    # ログ設定を追加（デバッグモードでもファイル出力）
    # ログディレクトリを作成
    os.makedirs('logs', exist_ok=True)

    # ファイルハンドラーを設定
    file_handler = logging.handlers.RotatingFileHandler(
        'logs/app.log', maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    ))
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.DEBUG)

    # コンソールハンドラーも追加
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    ))
    app.logger.addHandler(console_handler)

    # 初期化
    db.init_app(app)
    login_manager.init_app(app)
    csrf.init_app(app)
    login_manager.login_view = 'auth.login'

    # Blueprintの登録
    from app.views.auth import auth
    from app.views.user import user
    from app.views.slip_list import slip_list_bp
    from app.views.slip_detail import slip_detail
    from app.views.vendor import vendor
    from app.views.store import store
    from app.routes.purchase import bp as purchase_bp

    app.register_blueprint(auth)
    app.register_blueprint(user)
    app.register_blueprint(slip_list_bp)
    app.register_blueprint(slip_detail, url_prefix='/slip')
    app.register_blueprint(vendor)
    app.register_blueprint(store)
    app.register_blueprint(purchase_bp, url_prefix='/purchase')

    # デフォルトルートの設定
    @app.route('/')
    def index():
        return redirect(url_for('slip_list.list'))

    return app
