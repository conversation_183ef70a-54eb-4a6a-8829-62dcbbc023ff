#!/usr/bin/env python3
"""
ユーティリティクラス
"""
import configparser
import logging
import logging.config
import os
import smtplib
import jaconv
import atexit
import time
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, Any, Optional


class ConfigManager:
    """設定管理クラス"""
    
    def __init__(self, config_path: str = None):
        """
        Args:
            config_path: 設定ファイルのパス
        """
        if config_path is None:
            config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'config.ini')
        
        self.config_path = config_path
        self.config = configparser.ConfigParser()
        self.load_config()
    
    def load_config(self):
        """設定ファイルを読み込み"""
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"設定ファイルが見つかりません: {self.config_path}")
        
        self.config.read(self.config_path, encoding='utf-8')
    
    def get_database_config(self) -> Dict[str, Any]:
        """データベース設定を取得"""
        return dict(self.config['database'])
    
    def get_inventory_database_config(self) -> Dict[str, Any]:
        """在庫データベース設定を取得"""
        return dict(self.config['inventory_database'])
    
    def get_output_config(self) -> Dict[str, Any]:
        """出力設定を取得"""
        config = dict(self.config['output'])
        # パスの正規化
        for key in ['output_path', 'backup_path']:
            if key in config:
                config[key] = os.path.abspath(config[key])
        return config
    
    def get_system_config(self) -> Dict[str, Any]:
        """システム設定を取得"""
        config = dict(self.config['system'])
        # 数値型の変換
        if 'record_length' in config:
            config['record_length'] = int(config['record_length'])
        if 'max_retry_count' in config:
            config['max_retry_count'] = int(config['max_retry_count'])
        return config
    
    def get_email_config(self) -> Dict[str, Any]:
        """メール設定を取得"""
        if 'email' not in self.config:
            return {}
        
        config = dict(self.config['email'])
        # ポート番号の変換
        if 'port' in config:
            config['port'] = int(config['port'])
        # SSL設定の変換
        if 'use_ssl' in config:
            config['use_ssl'] = config['use_ssl'].lower() == 'true'
        return config


class RecordFormatter:
    """レコードフォーマッタークラス"""
    
    @staticmethod
    def format_string(value: str, length: int, align: str = 'L', fill_char: str = ' ') -> str:
        """文字列フィールドをフォーマット
        
        Args:
            value: 値
            length: フィールド長
            align: 配置（L=左寄せ、R=右寄せ）
            fill_char: 埋め文字
        
        Returns:
            フォーマット済み文字列
        """
        if value is None:
            value = ""
        
        # 文字列を指定長に調整
        if len(value) > length:
            value = value[:length]
        
        if align == 'R':
            return value.rjust(length, fill_char)
        else:
            return value.ljust(length, fill_char)
    
    @staticmethod
    def format_number(value: int, length: int, fill_char: str = '0') -> str:
        """数値フィールドをフォーマット
        
        Args:
            value: 値
            length: フィールド長
            fill_char: 埋め文字
        
        Returns:
            フォーマット済み文字列
        """
        if value is None:
            value = 0
        
        return str(value).rjust(length, fill_char)
    
    @staticmethod
    def format_date(date_value: datetime, format_str: str = '%Y%m%d') -> str:
        """日付フィールドをフォーマット
        
        Args:
            date_value: 日付値
            format_str: フォーマット文字列
        
        Returns:
            フォーマット済み日付文字列
        """
        if date_value is None:
            return ""
        
        return date_value.strftime(format_str)
    
    @staticmethod
    def convert_to_kana(text: str) -> str:
        """ひらがな・カタカナを半角カタカナに変換
        
        Args:
            text: 変換対象文字列
        
        Returns:
            半角カタカナ文字列
        """
        if not text:
            return ""
        
        # ひらがなをカタカナに変換してから半角カタカナに変換
        katakana = jaconv.hira2kata(text)
        hankaku_katakana = jaconv.zen2han(katakana, kana=True, ascii=False, digit=False)
        
        return hankaku_katakana


class FileNameGenerator:
    """ファイル名生成クラス"""
    
    @staticmethod
    def generate_shipment_filename(timestamp: datetime = None, vendor_code: str = None) -> str:
        """出荷ファイル名を生成
        
        Args:
            timestamp: タイムスタンプ
            vendor_code: ベンダーコード（7桁0埋め）
        
        Returns:
            ファイル名
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        if vendor_code:
            # vendor_codeを6桁0埋めでフォーマット
            vendor_code_formatted = str(vendor_code).zfill(6)
            return f"Shipment_{timestamp.strftime('%Y%m%d%H%M%S')}_{vendor_code_formatted}.DAT"
        else:
            return f"Shipment_{timestamp.strftime('%Y%m%d%H%M%S')}.DAT"
    
    @staticmethod
    def generate_backup_filename(original_filename: str, timestamp: datetime = None) -> str:
        """バックアップファイル名を生成
        
        Args:
            original_filename: 元ファイル名
            timestamp: タイムスタンプ
        
        Returns:
            バックアップファイル名
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        name, ext = os.path.splitext(original_filename)
        return f"{name}_backup_{timestamp.strftime('%Y%m%d%H%M%S')}{ext}"


class EmailNotifier:
    """メール通知クラス"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Args:
            config: メール設定
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def send_notification(self, subject: str, body: str, is_error: bool = False) -> bool:
        """通知メールを送信
        
        Args:
            subject: 件名
            body: 本文
            is_error: エラー通知かどうか
        
        Returns:
            送信成功フラグ
        """
        try:
            if not self.config or not self.config.get('enabled', False):
                self.logger.info("メール通知が無効化されています")
                return True
            
            # メール作成
            msg = MIMEMultipart()
            msg['From'] = self.config['from_address']
            msg['To'] = self.config['to_address']
            msg['Subject'] = subject
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # SMTP接続・送信
            if self.config.get('use_ssl', False):
                server = smtplib.SMTP_SSL(self.config['smtp_server'], self.config['port'])
            else:
                server = smtplib.SMTP(self.config['smtp_server'], self.config['port'])
                server.starttls()
            
            if self.config.get('username') and self.config.get('password'):
                server.login(self.config['username'], self.config['password'])
            
            server.send_message(msg)
            server.quit()
            
            self.logger.info(f"メール送信完了: {subject}")
            return True
            
        except Exception as e:
            self.logger.error(f"メール送信エラー: {e}")
            return False


def setup_logging(config_path: str = None):
    """ログ設定を初期化
    
    Args:
        config_path: ログ設定ファイルのパス
    """
    # 既存のハンドラーをクリーンアップ
    cleanup_logging()
    
    if config_path is None:
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'logging.conf')
    
    # ログディレクトリを作成
    log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    if os.path.exists(config_path):
        try:
            logging.config.fileConfig(config_path, disable_existing_loggers=False)
        except Exception as e:
            print(f"ログ設定ファイル読み込みエラー: {e}")
            # フォールバック設定
            setup_default_logging()
    else:
        # デフォルトログ設定
        setup_default_logging()
    
    # プロセス終了時のクリーンアップを登録
    register_cleanup()

def setup_default_logging():
    """デフォルトログ設定"""
    import logging.handlers
    
    # ルートロガー設定
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # フォーマッター
    formatter = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # コンソールハンドラー
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # ファイルハンドラー（ローテーション対応）
    try:
        file_handler = logging.handlers.RotatingFileHandler(
            'logs/shipment_batch.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    except Exception as e:
        print(f"ファイルハンドラー作成エラー: {e}")


def cleanup_logging():
    """ログハンドラーを適切に閉じる"""
    try:
        print("ログクリーンアップ開始...")
        
        # すべてのロガーのハンドラーを閉じる
        logger_dict = dict(logging.Logger.manager.loggerDict)
        for logger_name in logger_dict.keys():
            logger = logging.getLogger(logger_name)
            handlers_to_remove = list(logger.handlers)
            for handler in handlers_to_remove:
                try:
                    handler.acquire()
                    handler.flush()
                    handler.close()
                    logger.removeHandler(handler)
                except Exception as e:
                    print(f"ハンドラー閉じるエラー ({logger_name}): {e}")
                finally:
                    try:
                        handler.release()
                    except:
                        pass
        
        # ルートロガーのハンドラーも閉じる
        root_logger = logging.getLogger()
        root_handlers_to_remove = list(root_logger.handlers)
        for handler in root_handlers_to_remove:
            try:
                handler.acquire()
                handler.flush()
                handler.close()
                root_logger.removeHandler(handler)
            except Exception as e:
                print(f"ルートハンドラー閉じるエラー: {e}")
            finally:
                try:
                    handler.release()
                except:
                    pass
        
        # ログ設定をシャットダウン
        logging.shutdown()
        
        # ガベージコレクションを強制実行
        import gc
        gc.collect()
        
        # ファイルハンドルが確実に閉じられるまで待機
        import time
        time.sleep(1.0)
        
        print("ログクリーンアップ完了")
        
    except Exception as e:
        print(f"ログクリーンアップエラー: {e}")


def register_cleanup():
    """プロセス終了時のクリーンアップを登録"""
    atexit.register(cleanup_logging) 