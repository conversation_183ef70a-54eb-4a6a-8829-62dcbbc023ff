// 共通のユーティリティ関数

// 全角から半角への変換
export function convertFullToHalf(str) {
    return str.replace(/[！-～]/g, function(s) {
        return String.fromCharCode(s.charCodeAt(0) - 0xFEE0);
    }).replace(/　/g, ' ');
}

// メッセージ表示用のヘルパー関数
export function showMessage(elementId, message, type = 'success', duration = 2000) {
    const messageElement = document.getElementById(elementId);
    if (messageElement) {
        messageElement.textContent = message;
        messageElement.className = `alert alert-${type}`;
        messageElement.style.display = 'block';

        // 指定時間後にメッセージを非表示にする
        setTimeout(() => {
            messageElement.style.display = 'none';
        }, duration);
    }
}

// バリデーションエラーを表示する関数
export function showValidationErrors(errors) {
    // 既存のエラーメッセージを削除
    const existingAlert = document.querySelector('.validation-alert');
    if (existingAlert) {
        existingAlert.remove();
    }

    // エラーメッセージを作成
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger validation-alert';
    
    let errorHtml = '<h5>承認できません：</h5>';
    errorHtml += `<ul class="mb-1">
        ${errors.map(error => `<li>${error}</li>`).join('')}
    </ul>`;

    alertDiv.innerHTML = errorHtml;

    // エラーメッセージを表示
    const cardBody = document.querySelector('.card-body');
    if (cardBody) {
        cardBody.insertBefore(alertDiv, cardBody.firstChild);
    }
}

// 数値のフォーマット
export function formatNumber(number) {
    return new Intl.NumberFormat('ja-JP').format(number);
}

// CSRFトークンの取得
export function getCsrfToken() {
    return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
}

// 入力フィールドの自動変換設定
export function setupInputConverters() {
    document.querySelectorAll('input[type="text"]').forEach(input => {
        input.addEventListener('blur', function() {
            this.value = convertFullToHalf(this.value);
        });
    });
}
