[loggers]
keys=root,shipment_batch,database,file_generator,utils

[handlers]
keys=console<PERSON><PERSON><PERSON>,fileHandler

[formatters]
keys=simpleFormatter,detailFormatter

[logger_root]
level=INFO
handlers=consoleHandler,fileHandler

[logger_shipment_batch]
level=INFO
handlers=consoleHandler,fileHandler
qualname=__main__
propagate=0

[logger_database]
level=INFO
handlers=consoleHandler,fileHandler
qualname=database
propagate=0

[logger_file_generator]
level=DEBUG
handlers=consoleHandler,fileHandler
qualname=file_generator
propagate=0

[logger_utils]
level=INFO
handlers=consoleHandler,fileHandler
qualname=utils
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=DEBUG
formatter=simpleFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=logging.handlers.RotatingFileHandler
level=DEBUG
formatter=detailFormatter
args=('logs/shipment_batch.log', 'a', 10485760, 5, 'utf-8')

[formatter_simpleFormatter]
format=%(asctime)s [%(levelname)s] %(name)s: %(message)s
datefmt=%Y-%m-%d %H:%M:%S

[formatter_detailFormatter]
format=%(asctime)s [%(levelname)s] %(name)s: %(message)s
datefmt=%Y-%m-%d %H:%M:%S 