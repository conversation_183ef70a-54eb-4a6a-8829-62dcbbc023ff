#!/usr/bin/env python3

# 明細レコードの構造を確認
filename = 'output/Shipment_20250526180742_001837.DAT'

with open(filename, 'r', encoding='shift_jis') as f:
    lines = f.readlines()

# 最初のNNN4レコードを詳細分析
for i, line in enumerate(lines):
    line_content = line.rstrip('\n\r')
    first_half = line_content[:128] if len(line_content) >= 128 else line_content
    
    if first_half.startswith('NNN4'):
        print(f'行{i+1} NNN4レコード詳細分析:')
        print(f'レコード長: {len(first_half)}')
        print()
        
        # 各フィールドの位置を確認
        pos = 0
        print(f'{pos:2d}-{pos+1:2d}: データ種別 = "{first_half[pos:pos+2]}"')
        pos += 2
        print(f'{pos:2d}-{pos+1:2d}: レコード種別 = "{first_half[pos:pos+2]}"')
        pos += 2
        print(f'{pos:2d}-{pos+4:2d}: FILLER1 = "{first_half[pos:pos+5]}"')
        pos += 5
        print(f'{pos:2d}-{pos+1:2d}: 行No = "{first_half[pos:pos+2]}"')
        pos += 2
        print(f'{pos:2d}-{pos+13:2d}: 商品コード = "{first_half[pos:pos+14]}"')
        pos += 14
        print(f'{pos:2d}-{pos+28:2d}: 商品名 = "{first_half[pos:pos+29]}"')
        pos += 29
        print(f'{pos:2d}-{pos+3:2d}: 発注単位 = "{first_half[pos:pos+4]}"')  # 54-57桁目
        pos += 4
        print(f'{pos:2d}-{pos+1:2d}: 単位区分 = "{first_half[pos:pos+2]}"')  # 58-59桁目
        pos += 2
        print(f'{pos:2d}-{pos+5:2d}: 発注数量 = "{first_half[pos:pos+6]}"')  # 60-65桁目
        pos += 6
        print(f'{pos:2d}-{pos+5:2d}: 入荷予定数 = "{first_half[pos:pos+6]}"')  # 66-71桁目
        pos += 6
        print(f'{pos:2d}-{pos+7:2d}: 原単価 = "{first_half[pos:pos+8]}"')  # 72-79桁目
        pos += 8
        print(f'{pos:2d}-{pos+7:2d}: 原価金額 = "{first_half[pos:pos+8]}"')  # 80-87桁目
        pos += 8
        print(f'{pos:2d}-{pos+5:2d}: 売価 = "{first_half[pos:pos+6]}"')  # 88-93桁目
        pos += 6
        print(f'{pos:2d}-{pos+7:2d}: 売価金額 = "{first_half[pos:pos+8]}"')  # 94-101桁目
        pos += 8
        print(f'{pos:2d}-{pos+3:2d}: 単位当たり入数 = "{first_half[pos:pos+4]}"')  # 102-105桁目
        pos += 4
        print(f'{pos:2d}-{pos+3:2d}: ケース代 = "{first_half[pos:pos+4]}"')  # 106-109桁目
        pos += 4
        print(f'{pos:2d}-{pos+7:2d}: クラス = "{first_half[pos:pos+8]}"')  # 110-117桁目
        pos += 8
        print(f'{pos:2d}-{pos+4:2d}: FILLER = "{first_half[pos:pos+5]}"')  # 118-122桁目
        pos += 5
        print(f'{pos:2d}-{pos+4:2d}: データシリアルNo = "{first_half[pos:pos+5]}"')  # 123-127桁目
        
        print(f'\n現在の位置: {pos+5} (期待値: 128)')
        break 