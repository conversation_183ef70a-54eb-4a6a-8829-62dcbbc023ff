{% extends "base.html" %}

{% block head %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/store_settings.css') }}">
{% endblock %}

{% block content %}
<div class="store-settings-page">

    <!-- 新規登録フォーム -->
    <div class="card mb-4">
        <div class="card-header">
            <h3 class="card-title">新規店舗登録</h3>
        </div>
        <div class="card-body">
            <form id="storeForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="row">
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label for="store_code" class="form-label">店舗コード</label>
                            <input type="text" class="form-control" id="store_code" name="store_code" required pattern="[0-9]{2}" maxlength="2">
                            <div class="form-text">2桁の数字で入力してください</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="store_name" class="form-label">店舗名</label>
                            <input type="text" class="form-control" id="store_name" name="store_name" required maxlength="50">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="store_name_kana" class="form-label">店舗名カナ</label>
                            <input type="text" class="form-control" id="store_name_kana" name="store_name_kana" maxlength="50">
                            <div class="form-text">半角カタカナで入力してください（任意）</div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">登録</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 店舗一覧 -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">店舗一覧</h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>店舗コード</th>
                            <th>店舗名</th>
                            <th>店舗名カナ</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for store in stores %}
                        <tr>
                            <td>{{ store.store_code }}</td>
                            <td>
                                <span class="store-name">{{ store.store_name }}</span>
                                <input type="text" class="form-control edit-name d-none" value="{{ store.store_name }}" maxlength="50">
                            </td>
                            <td>
                                <span class="store-name-kana">{{ store.store_name_kana or '' }}</span>
                                <input type="text" class="form-control edit-name-kana d-none" value="{{ store.store_name_kana or '' }}" maxlength="50">
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary edit-btn">
                                    <i class="bi bi-pencil"></i> 編集
                                </button>
                                <button class="btn btn-sm btn-outline-success save-btn d-none" data-store-id="{{ store.id }}">
                                    <i class="bi bi-check"></i> 保存
                                </button>
                                <button class="btn btn-sm btn-outline-secondary cancel-btn d-none">
                                    <i class="bi bi-x"></i> キャンセル
                                </button>
                                <button class="btn btn-sm btn-outline-danger delete-btn" data-store-id="{{ store.id }}">
                                    <i class="bi bi-trash"></i> 削除
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script type="module">
    import { initializeStoreSettings } from '/static/js/pages/store/settings.js';
    import { initializeStoreList } from '/static/js/pages/store/list.js';

    document.addEventListener('DOMContentLoaded', function() {
        initializeStoreSettings();
        initializeStoreList();
    });
</script>
{% endblock %} 