#!/usr/bin/env python3
"""
テスト用データ確認スクリプト
"""
import sys
import os

# プロジェクトルートをパスに追加
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils import ConfigManager
from database import DatabaseManager

def check_test_data():
    """テスト用データの存在を確認する"""
    print("=== テスト用データ確認開始 ===")
    
    try:
        # 設定管理初期化
        config = ConfigManager()
        
        # データベース管理初期化
        db_manager = DatabaseManager(config)
        db_manager.connect()
        
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 伝票データの確認
            print("伝票データの確認:")
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM dbo.purchase_slip
                WHERE is_deleted = 0
            """)
            total_slips = cursor.fetchone().total_count
            print(f"  総伝票数: {total_slips}")
            
            cursor.execute("""
                SELECT COUNT(*) as approved_count
                FROM dbo.purchase_slip
                WHERE is_deleted = 0 AND status = 'approved'
            """)
            approved_slips = cursor.fetchone().approved_count
            print(f"  承認済み伝票数: {approved_slips}")
            
            cursor.execute("""
                SELECT COUNT(*) as unsent_count
                FROM dbo.purchase_slip
                WHERE is_deleted = 0 AND status = 'approved' AND send_flg = 0
            """)
            unsent_slips = cursor.fetchone().unsent_count
            print(f"  未送信の承認済み伝票数: {unsent_slips}")
            
            # 明細データの確認
            print("\n明細データの確認:")
            cursor.execute("""
                SELECT COUNT(*) as detail_count
                FROM dbo.purchase_slip_detail psd
                INNER JOIN dbo.purchase_slip ps ON psd.slip_id = ps.id
                WHERE ps.is_deleted = 0 AND ps.status = 'approved' AND ps.send_flg = 0
            """)
            detail_count = cursor.fetchone().detail_count
            print(f"  送信対象明細数: {detail_count}")
            
            # section_idがある明細の確認
            cursor.execute("""
                SELECT COUNT(*) as section_id_count
                FROM dbo.purchase_slip_detail psd
                INNER JOIN dbo.purchase_slip ps ON psd.slip_id = ps.id
                WHERE ps.is_deleted = 0 AND ps.status = 'approved' AND ps.send_flg = 0
                  AND psd.section_id IS NOT NULL
            """)
            section_id_count = cursor.fetchone().section_id_count
            print(f"  section_idがある明細数: {section_id_count}")
            
            # サンプルデータの表示
            if unsent_slips > 0:
                print("\nサンプル伝票データ:")
                cursor.execute("""
                    SELECT TOP 3 id, slip_number, vendor_code, vendor_name, status, send_flg
                    FROM dbo.purchase_slip
                    WHERE is_deleted = 0 AND status = 'approved' AND send_flg = 0
                    ORDER BY id
                """)
                
                sample_slips = cursor.fetchall()
                for slip in sample_slips:
                    print(f"  ID: {slip.id}, 伝票番号: {slip.slip_number}, 取引先: {slip.vendor_code}, ステータス: {slip.status}")
                    
                    # 明細データも表示
                    cursor.execute("""
                        SELECT line_number, product_code, section_id
                        FROM dbo.purchase_slip_detail
                        WHERE slip_id = ?
                        ORDER BY line_number
                    """, (slip.id,))
                    
                    details = cursor.fetchall()
                    for detail in details:
                        print(f"    明細 {detail.line_number}: 商品コード={detail.product_code}, section_id={detail.section_id}")
        
        print("=== テスト用データ確認完了 ===")
        
    except Exception as e:
        print(f"確認中にエラーが発生: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if 'db_manager' in locals():
            db_manager.disconnect()

if __name__ == "__main__":
    check_test_data() 