#!/usr/bin/env python3
"""
出荷データ出力バッチプログラム
"""
import logging
import sys
import os
from datetime import datetime
from typing import List

# プロジェクトルートをパスに追加
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import PurchaseSlip
from utils import ConfigManager, EmailNotifier, setup_logging, cleanup_logging, register_cleanup
from database import DatabaseManager
from file_generator import FileGenerator


class ShipmentBatch:
    """出荷データ出力バッチクラス"""
    
    def __init__(self, config_path: str = None):
        """
        Args:
            config_path: 設定ファイルのパス
        """
        print("ShipmentBatch初期化開始...")
        
        try:
            # ログ設定初期化
            print("ログ設定初期化中...")
            setup_logging()
            self.logger = logging.getLogger(__name__)
            print("ログ設定初期化完了")
            
            # 設定管理初期化
            print("設定管理初期化中...")
            self.config = ConfigManager(config_path)
            print("設定管理初期化完了")
            
            # 各種マネージャー初期化
            print("データベースマネージャー初期化中...")
            self.db_manager = DatabaseManager(self.config)
            print("データベースマネージャー初期化完了")
            
            print("ファイルジェネレーター初期化中...")
            self.file_generator = FileGenerator(self.config, self.db_manager)
            print("ファイルジェネレーター初期化完了")
            
            # メール通知設定
            print("メール通知設定中...")
            email_config = self.config.get_email_config()
            self.email_notifier = EmailNotifier(email_config) if email_config else None
            print(f"メール通知設定完了: {'有効' if self.email_notifier else '無効'}")
            
            self.logger.info("出荷データ出力バッチ初期化完了")
            print("ShipmentBatch初期化完了")
            
        except Exception as e:
            print(f"ShipmentBatch初期化エラー: {e}")
            print(f"エラータイプ: {type(e).__name__}")
            import traceback
            traceback.print_exc()
            raise
    
    def run(self) -> bool:
        """バッチ処理を実行
        
        Returns:
            実行成功フラグ
        """
        start_time = datetime.now()
        self.logger.info("=" * 50)
        self.logger.info("出荷データ出力バッチ開始")
        self.logger.info(f"開始時刻: {start_time}")
        
        try:
            # データベース接続
            self.db_manager.connect()
            
            # 送信対象データ取得
            target_slips = self._get_target_data()
            
            if not target_slips:
                self.logger.info("送信対象データがありません")
                self._send_notification("出荷データ出力バッチ完了", "送信対象データがありませんでした。")
                return True
            
            # vendor_code別にグループ化
            vendor_groups = self._group_by_vendor_code(target_slips)
            
            output_files = []
            all_processed_slips = []
            
            # vendor_code別にファイル生成
            for vendor_code, slips in vendor_groups.items():
                self.logger.info(f"vendor_code {vendor_code}: {len(slips)}件の処理開始")
                
                # ファイル生成
                output_file = self._generate_file(slips, start_time, vendor_code)
                output_files.append(output_file)
                all_processed_slips.extend(slips)
                
                # バックアップ作成
                self._create_backup(output_file, start_time)
                
                self.logger.info(f"vendor_code {vendor_code}: 処理完了")
            
            # データベース更新（全件まとめて）
            self._update_database(all_processed_slips)
            
            # 成功通知
            self._send_success_notification(all_processed_slips, output_files, start_time)
            
            end_time = datetime.now()
            processing_time = end_time - start_time
            
            self.logger.info(f"出荷データ出力バッチ正常終了")
            self.logger.info(f"処理時間: {processing_time}")
            self.logger.info(f"処理件数: {len(all_processed_slips)}件")
            self.logger.info(f"出力ファイル数: {len(output_files)}件")
            self.logger.info("=" * 50)
            
            return True
            
        except Exception as e:
            self.logger.error(f"出荷データ出力バッチエラー: {e}")
            self._send_error_notification(e, start_time)
            return False
            
        finally:
            # データベース接続切断
            self.db_manager.disconnect()
            
            # ログの最終出力
            self.logger.info("バッチ処理終了")
    
    def _get_target_data(self) -> List[PurchaseSlip]:
        """送信対象データを取得
        
        Returns:
            送信対象の仕入伝票リスト
        """
        self.logger.info("送信対象データ取得開始")
        
        try:
            # 送信対象伝票取得
            slips = self.db_manager.get_target_slips()
            
            if slips:
                # データ整合性チェック
                slip_ids = [slip.id for slip in slips]
                if not self.db_manager.validate_data_integrity(slip_ids):
                    raise Exception("データ整合性チェックに失敗しました")
                
                self.logger.info(f"送信対象データ取得完了: {len(slips)}件")
                
                # 統計情報ログ出力
                total_details = sum(len(slip.details) for slip in slips)
                self.logger.info(f"  - 伝票数: {len(slips)}件")
                self.logger.info(f"  - 明細数: {total_details}件")
            else:
                self.logger.info("送信対象データなし")
            
            return slips
            
        except Exception as e:
            self.logger.error(f"送信対象データ取得エラー: {e}")
            raise
    
    def _group_by_vendor_code(self, slips: List[PurchaseSlip]) -> dict:
        """vendor_code別にグループ化
        
        Args:
            slips: 仕入伝票リスト
        
        Returns:
            vendor_code別の伝票辞書
        """
        from collections import defaultdict
        
        vendor_groups = defaultdict(list)
        
        for slip in slips:
            vendor_code = slip.vendor_code or "0000000"  # vendor_codeがNoneの場合のデフォルト値
            vendor_groups[vendor_code].append(slip)
        
        self.logger.info(f"vendor_code別グループ化完了: {len(vendor_groups)}グループ")
        for vendor_code, group_slips in vendor_groups.items():
            self.logger.info(f"  - vendor_code {vendor_code}: {len(group_slips)}件")
        
        return dict(vendor_groups)
    
    def _generate_file(self, slips: List[PurchaseSlip], timestamp: datetime, vendor_code: str = None) -> str:
        """ファイルを生成
        
        Args:
            slips: 仕入伝票リスト
            timestamp: タイムスタンプ
            vendor_code: ベンダーコード
        
        Returns:
            生成されたファイルのパス
        """
        vendor_code_display = vendor_code or "不明"
        self.logger.info(f"ファイル生成開始 (vendor_code: {vendor_code_display})")
        
        try:
            output_file = self.file_generator.generate_shipment_file(slips, timestamp, vendor_code)
            
            # 統計情報取得
            stats = self.file_generator.get_file_statistics()
            
            self.logger.info(f"ファイル生成完了: {os.path.basename(output_file)}")
            self.logger.info(f"  - 総レコード数: {stats.total_records}件")
            self.logger.info(f"  - ヘッダー: {stats.header_records}件")
            self.logger.info(f"  - 伝票ヘッダー: {stats.slip_header_records}件")
            self.logger.info(f"  - 明細: {stats.detail_records}件")
            self.logger.info(f"  - トレーラー: {stats.trailer_records}件")
            
            return output_file
            
        except Exception as e:
            self.logger.error(f"ファイル生成エラー (vendor_code: {vendor_code_display}): {e}")
            raise
    
    def _update_database(self, slips: List[PurchaseSlip]) -> None:
        """データベースを更新
        
        Args:
            slips: 処理済み仕入伝票リスト
        """
        self.logger.info("データベース更新開始")
        
        try:
            # トランザクション開始
            self.db_manager.begin_transaction()
            
            # 送信ステータス更新
            slip_ids = [slip.id for slip in slips]
            if not self.db_manager.update_send_status(slip_ids):
                raise Exception("送信ステータス更新に失敗しました")
            
            # トランザクションコミット
            self.db_manager.commit_transaction()
            
            self.logger.info(f"データベース更新完了: {len(slip_ids)}件")
            
        except Exception as e:
            # トランザクションロールバック
            self.db_manager.rollback_transaction()
            self.logger.error(f"データベース更新エラー: {e}")
            raise
    
    def _create_backup(self, output_file: str, timestamp: datetime) -> None:
        """バックアップを作成
        
        Args:
            output_file: 出力ファイルのパス
            timestamp: タイムスタンプ
        """
        self.logger.info("バックアップ作成開始")
        
        try:
            backup_file = self.file_generator.create_backup(output_file, timestamp)
            self.logger.info(f"バックアップ作成完了: {os.path.basename(backup_file)}")
            
        except Exception as e:
            # バックアップ失敗は警告レベル（処理は継続）
            self.logger.warning(f"バックアップ作成エラー: {e}")
    
    def _send_success_notification(self, slips: List[PurchaseSlip], output_files: list, start_time: datetime) -> None:
        """成功通知を送信
        
        Args:
            slips: 処理済み仕入伝票リスト
            output_files: 出力ファイルのパスリスト
            start_time: 処理開始時刻
        """
        if not self.email_notifier:
            return
        
        try:
            end_time = datetime.now()
            processing_time = end_time - start_time
            
            subject = "出荷データ出力バッチ正常終了"
            
            # 出力ファイル一覧を作成
            file_list = "\n".join([f"・{os.path.basename(file)}" for file in output_files])
            
            body = f"""
出荷データ出力バッチが正常に終了しました。

【処理結果】
・処理開始時刻: {start_time.strftime('%Y-%m-%d %H:%M:%S')}
・処理終了時刻: {end_time.strftime('%Y-%m-%d %H:%M:%S')}
・処理時間: {processing_time}
・処理件数: {len(slips)}件
・出力ファイル数: {len(output_files)}件

【出力ファイル一覧】
{file_list}

【統計情報】
・総レコード数: {self.file_generator.get_file_statistics().total_records}件
・明細数: {self.file_generator.get_file_statistics().detail_records}件

処理が正常に完了しました。
            """.strip()
            
            self.email_notifier.send_notification(subject, body, is_error=False)
            
        except Exception as e:
            self.logger.warning(f"成功通知送信エラー: {e}")
    
    def _send_error_notification(self, error: Exception, start_time: datetime) -> None:
        """エラー通知を送信
        
        Args:
            error: 発生したエラー
            start_time: 処理開始時刻
        """
        if not self.email_notifier:
            return
        
        try:
            end_time = datetime.now()
            processing_time = end_time - start_time
            
            subject = "出荷データ出力バッチエラー"
            
            body = f"""
出荷データ出力バッチでエラーが発生しました。

【エラー情報】
・処理開始時刻: {start_time.strftime('%Y-%m-%d %H:%M:%S')}
・エラー発生時刻: {end_time.strftime('%Y-%m-%d %H:%M:%S')}
・処理時間: {processing_time}
・エラー内容: {str(error)}

システム管理者に連絡してください。
            """.strip()
            
            self.email_notifier.send_notification(subject, body, is_error=True)
            
        except Exception as e:
            self.logger.warning(f"エラー通知送信エラー: {e}")
    
    def _send_notification(self, subject: str, body: str) -> None:
        """通知を送信
        
        Args:
            subject: 件名
            body: 本文
        """
        if not self.email_notifier:
            return
        
        try:
            self.email_notifier.send_notification(subject, body)
        except Exception as e:
            self.logger.warning(f"通知送信エラー: {e}")


def main():
    """メイン関数"""
    batch = None
    try:
        print("=" * 50)
        print("出荷データ出力バッチ開始")
        print(f"開始時刻: {datetime.now()}")
        print("=" * 50)
        
        # コマンドライン引数から設定ファイルパスを取得
        config_path = sys.argv[1] if len(sys.argv) > 1 else None
        print(f"設定ファイルパス: {config_path or 'デフォルト'}")
        
        # 作業ディレクトリの確認
        print(f"作業ディレクトリ: {os.getcwd()}")
        print(f"スクリプトディレクトリ: {os.path.dirname(os.path.abspath(__file__))}")
        
        # バッチ初期化
        print("バッチクラス初期化開始...")
        batch = ShipmentBatch(config_path)
        print("バッチクラス初期化完了")
        
        # バッチ実行
        print("バッチ実行開始...")
        success = batch.run()
        print(f"バッチ実行完了: {'成功' if success else '失敗'}")
        
        # データベース接続を明示的に閉じる
        if batch and batch.db_manager:
            print("データベース接続を閉じています...")
            batch.db_manager.close()
            print("データベース接続を閉じました")
        
        # 終了コード設定
        exit_code = 0 if success else 1
        print(f"終了コード: {exit_code}")
        sys.exit(exit_code)
        
    except Exception as e:
        print(f"バッチ実行エラー: {e}")
        print(f"エラータイプ: {type(e).__name__}")
        
        # スタックトレースを出力
        import traceback
        print("スタックトレース:")
        traceback.print_exc()
        
        # データベース接続を明示的に閉じる
        if batch and batch.db_manager:
            try:
                print("エラー時のデータベース接続クローズ...")
                batch.db_manager.close()
                print("データベース接続をクローズしました")
            except Exception as close_error:
                print(f"データベース接続クローズエラー: {close_error}")
        
        sys.exit(1)
    finally:
        try:
            # ログハンドラーを適切に閉じる
            print("ログクリーンアップ開始...")
            cleanup_logging()
            print("ログクリーンアップ完了")
        except Exception as cleanup_error:
            print(f"ログクリーンアップエラー: {cleanup_error}")
        
        print("=" * 50)
        print("バッチ処理終了")
        print("=" * 50)


if __name__ == "__main__":
    main() 