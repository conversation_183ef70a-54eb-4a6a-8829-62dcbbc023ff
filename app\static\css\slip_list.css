/* slip_list.html 専用のスタイル */

/* 検索条件カード */
.slip-list-page .card-header {
    padding: 0.75rem 1.25rem;
}

/* 入力欄の幅調整 */
.slip-list-page input[name="date_from"],
.slip-list-page input[name="date_to"] {
    text-align: left;
    max-width: 150px;
} 

.slip-list-page input[name="amount_from"],
.slip-list-page input[name="amount_to"] {
    text-align: right;
    max-width: 150px;
} 

.slip-list-page input[name="slip_number"],
.slip-list-page input[name="product_code"]{
    max-width: 200px;
}

.slip-list-page input[name="company_code"],
.slip-list-page input[name="vendor_code"],
.slip-list-page input[name="department_code"] {
    max-width: 100px;
}

.slip-list-page input[name="product_name"]{
    max-width: 220px;
}

.slip-list-page select[name="store_code"] ,
.slip-list-page select[name="sort_by"] {
    max-width: 200px;
}

.slip-list-page input[name="created_at"] ,
.slip-list-page select[name="status"]{
    max-width: 150px;
}

/* テーブルのスタイル */
.slip-list-page .table {
    margin-bottom: 0;
}

.slip-list-page .table th {
    background-color: var(--custom-card-bg);
    white-space: nowrap;
    padding: 0.5rem;
}

.slip-list-page .table td {
    padding: 0.5rem;
    vertical-align: middle;
}

/* 金額列の右寄せ */
.slip-list-page .table td.text-end {
    font-family: monospace;
    font-size: 0.9rem;
}

/* 送信済み伝票の行スタイル */
.slip-list-page .table tr.slip-sent {
    background-color: #f8f9fa !important; /* 薄いグレー */
    color: #6c757d; /* 文字色も薄く */
}

.slip-list-page .table tr.slip-sent:hover {
    background-color: #e9ecef !important; /* ホバー時のグレー */
}

/* 送信済み伝票のチェックボックスを無効化 */
.slip-list-page .table tr.slip-sent input[type="checkbox"] {
    opacity: 0.5;
    cursor: not-allowed;
}

/* ステータスバッジのスタイル */
.slip-list-page .badge {
    font-size: 0.8rem;
    padding: 0.35em 0.65em;
}

/* 検索ボタンエリアのスタイル */
.slip-list-page .btn-primary,
.slip-list-page .btn-secondary {
    min-width: 50px;
}

/* クリアボタンのスタイル */
.slip-list-page .btn-clear {
    min-width: 50px; /* 必要に応じて幅を調整 */
}

/* レスポンシブ対応 */
@media (max-width: 768px) {
    .slip-list-page .card-body {
        padding: 0.75rem;
    }
}

/* その他、slip_list.html 専用のスタイル */
/* 必要に応じて追加 */

.slip-list-page .table-responsive {
    max-height: 600px; /* 必要に応じて高さを調整 */
    overflow-y: auto;
}
