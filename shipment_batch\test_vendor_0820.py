#!/usr/bin/env python3
"""
vendor_code 0820のテスト用スクリプト
"""
import sys
import os
from datetime import datetime

# プロジェクトルートをパスに追加
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils import ConfigManager, setup_logging
from database import DatabaseManager
from file_generator import FileGenerator

def main():
    setup_logging()
    
    config = ConfigManager()
    db_manager = DatabaseManager(config)
    file_generator = FileGenerator(config)
    
    try:
        # データベース接続
        db_manager.connect()
        
        # 送信対象データ取得
        all_slips = db_manager.get_target_slips()
        
        # vendor_code 0820のみを抽出
        vendor_0820_slips = [slip for slip in all_slips if slip.vendor_code == '0820']
        
        print(f"vendor_code 0820の伝票数: {len(vendor_0820_slips)}件")
        
        if vendor_0820_slips:
            # ファイル生成
            timestamp = datetime.now()
            output_file = file_generator.generate_shipment_file(vendor_0820_slips, timestamp, '0820')
            print(f"ファイル生成完了: {output_file}")
        else:
            print("vendor_code 0820の伝票がありません")
            
    except Exception as e:
        print(f"エラー: {e}")
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    main() 