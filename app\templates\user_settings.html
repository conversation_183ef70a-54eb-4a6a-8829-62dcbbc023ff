{% extends "base.html" %}

{% block head %}
{{ super() }}
<meta name="csrf-token" content="{{ csrf_token() }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/user_settings.css') }}">
{% endblock %}

{% block scripts %}
<script type="module">
    import { initializeUserSettings } from "{{ url_for('static', filename='js/pages/user/settings.js') }}";
    import { loadUserList } from "{{ url_for('static', filename='js/pages/user/list.js') }}";

    // グローバル変数の設定
    window.currentUser = "{{ current_user.username }}";
    window.isAdmin = "{{ current_user.adminuser }}" === "True";

    document.addEventListener('DOMContentLoaded', function() {
        initializeUserSettings();
    });
</script>
{% endblock %}

{% block content %}
<div class="user-settings-page">
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">ユーザー管理</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <form id="userForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">ユーザー名</label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   {% if not current_user.adminuser %}value="{{ current_user.username }}" readonly{% endif %} required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">パスワード</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="form-text password-requirements">
                                パスワードは以下の要件を満たす必要があります：
                                <ul>
                                    <li>8文字以上</li>
                                    <li>英大文字を含む</li>
                                    <li>英小文字を含む</li>
                                    <li>数字を含む</li>
                                </ul>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">パスワード（確認）</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        {% if current_user.adminuser %}
                        <button type="submit" class="btn btn-primary" id="registerUserBtn">ユーザーを登録</button>
                        {% endif %}
                        <button type="submit" class="btn btn-primary" id="changePasswordBtn">パスワード変更</button>
                    </form>
                    <div id="userFormMessage" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- ユーザー一覧 -->
    <div class="card mb-4" id="userListContainer">
        <div class="card-header">
            <h5 class="card-title mb-0">ユーザー一覧</h5>
        </div>
        <div class="card-body">
            <div id="userListMessage" style="display: none;"></div>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ユーザー名</th>
                            <th>承認権限</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="userList">
                        <!-- JavaScriptで動的に追加 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}