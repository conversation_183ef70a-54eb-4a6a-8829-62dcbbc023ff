// 履歴更新機能をインポート
import { updateHistoryList } from './history.js';

// 共通のフェッチ設定
const fetchConfig = {
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
    },
    credentials: 'include'
};

// レスポンスの共通チェック処理
async function handleResponse(response) {
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
        if (response.redirected || response.status === 401 || response.status === 403) {
            window.location.href = '/login';
            return null;
        }
        throw new Error('セッションが切れている可能性があります。ページを更新してください。');
    }

    const result = await response.json();
    if (!response.ok) {
        throw new Error(result.error || '更新に失敗しました');
    }
    return result;
}

// 明細行の処理を管理するモジュール
export const DetailRowManager = {
    // 明細行のエンドポイントマッピング
    detailFields: {
        product_code: 'product',
        quantity: 'quantity',
        unit_cost: 'unit-cost',
        unit_price: 'unit-price'
    },

    // 明細行の初期化
    initialize() {
        this.sortDetailRows();
        this.setupDetailRowEventListeners();
        this.setupAmountCalculation();
    },

    // 明細行のソート処理を追加
    sortDetailRows() {
        const tbody = document.querySelector('.table tbody');
        if (!tbody) return;

        const rows = Array.from(tbody.querySelectorAll('.slip-detail-row'));
        rows.sort((a, b) => {
            const aLineNumber = parseInt(a.querySelector('td:first-child').textContent);
            const bLineNumber = parseInt(b.querySelector('td:first-child').textContent);
            return aLineNumber - bLineNumber;
        });

        rows.forEach(row => tbody.appendChild(row));
    },

    // 明細行のイベントリスナー設定
    setupDetailRowEventListeners() {
        document.querySelectorAll('.slip-detail-row').forEach(row => {
            const detailId = row.dataset.detailId;
            if (!detailId) return;
            this.initializeDetailInputs(row, detailId);
        });
    },

    // 金額計算のイベントリスナー設定
    setupAmountCalculation() {
        document.querySelectorAll('input[name="quantity"], input[name="unit_cost"], input[name="unit_price"]')
            .forEach(input => {
                input.addEventListener('change', () => {
                    const row = input.closest('.slip-detail-row');
                    if (row) {
                        this.updateRowAmounts(row);
                        this.updateTotalAmounts();
                    }
                });
            });
    },

    // 各入力フィールドの初期化
    initializeDetailInputs(row, detailId) {
        const inputConfigs = {
            'product_code': {
                validate: (value) => {
                    // 数値のみで構成されているかチェック
                    if (!/^\d+$/.test(value)) return false;
                    // 13桁以下であることをチェック（0埋め前）
                    if (value.length > 13) return false;
                    return true;
                },
                errorMessage: '商品コードは数字のみで入力してください'
            },
            'quantity': {
                validate: (value) => value >= 0 && value <= 999999,
                errorMessage: '数量は0以上999999以下で入力してください'
            },
            'unit_cost': {
                validate: (value) => value >= 1.00 && value <= 999999.99,
                errorMessage: '原単価は1.00以上999999.99以下で入力してください'
            },
            'unit_price': {
                validate: (value) => value >= 0 && value <= 9999999,
                errorMessage: '売単価は0以上9999999以下で入力してください'
            }
        };

        Object.entries(inputConfigs).forEach(([fieldName, config]) => {
            const input = row.querySelector(`[name="${fieldName}"]`);
            if (input && !input.dataset.hasListener) {
                this.setupInputEventListener(input, fieldName, detailId, config);
            }
        });
    },

    // 入力フィールドのイベントリスナー設定
    setupInputEventListener(input, fieldName, detailId, config) {
        input.dataset.hasListener = 'true';
        input.dataset.originalValue = input.value;

        input.addEventListener('change', async function() {
            if (this.dataset.processing === 'true') return;

            try {
                this.dataset.processing = 'true';
                let value = this.value.trim();

                if (value === '') {
                    alert(`${fieldName}を入力してください`);
                    this.value = this.dataset.originalValue;
                    return;
                }

                // 商品コードの場合の特別処理
                if (fieldName === 'product_code') {
                    if (!/^\d+$/.test(value)) {
                        alert('商品コードは数字のみで入力してください');
                        this.value = this.dataset.originalValue;
                        return;
                    }
                    // 13桁未満の場合は0埋め
                    if (value.length < 13) {
                        value = value.padStart(13, '0');
                        this.value = value;
                    }
                }

                if (!config.validate(value)) {
                    alert(config.errorMessage);
                    this.value = this.dataset.originalValue;
                    return;
                }

                const slipId = document.querySelector('.slip-detail-form').dataset.slipId;
                const result = await DetailRowManager.updateDetailField(slipId, detailId, fieldName, value);
                
                if (result?.status === 'success') {
                    this.dataset.originalValue = value;
                    if (result.related_fields) {
                        DetailRowManager.updateRelatedFields(this.closest('.slip-detail-row'), result.related_fields);
                    }
                }
            } catch (error) {
                alert(error.message);
                console.error(`明細の${fieldName}更新に失敗:`, error);
                this.value = this.dataset.originalValue;
            } finally {
                delete this.dataset.processing;
            }
        });
    },

    // APIを呼び出す共通メソッド
    async callUpdateApi(slipId, detailId, fieldName, value) {
        const endpoint = this.detailFields[fieldName];
        if (!endpoint) throw new Error('Invalid field name');

        // 行番号を取得
        const row = document.querySelector(`.slip-detail-row[data-detail-id="${detailId}"]`);
        const lineNumber = row ? row.querySelector('td:first-child').textContent : '';
        const oldValue = row ? row.querySelector(`[name="${fieldName}"]`).dataset.originalValue : '';

        // フィールド名の日本語マッピング
        const fieldLabels = {
            'product_code': '商品コード',
            'quantity': '数量',
            'unit_cost': '原単価',
            'unit_price': '売単価'
        };

        const response = await fetch(`/slip/${slipId}/details/${detailId}/${endpoint}`, {
            ...fetchConfig,
            method: 'PUT',
            body: JSON.stringify({ 
                [fieldName]: value,
                line_number: lineNumber,
                old_value: oldValue,
                field_label: fieldLabels[fieldName]
            })
        });

        const result = await handleResponse(response);
        if (result?.status === 'success') {
            // 履歴一覧を更新
            await updateHistoryList(slipId);
        }
        return result;
    },

    // 共通のフィールド更新メソッド
    async updateDetailField(slipId, detailId, fieldName, value) {
        try {
            const result = await this.callUpdateApi(slipId, detailId, fieldName, value);
            if (result?.status === 'success') {
                await this.handleSuccessfulUpdate(slipId, detailId, fieldName, value, result);
            }
            return result;
        } catch (error) {
            console.error(`${fieldName}更新エラー:`, error);
            throw error;
        }
    },

    // 成功時の処理をまとめる
    async handleSuccessfulUpdate(slipId, detailId, fieldName, value, result) {
        const row = document.querySelector(`.slip-detail-row[data-detail-id="${detailId}"]`);
        if (!row) return;

        // 金額関連フィールドの更新
        if (['quantity', 'unit_cost', 'unit_price'].includes(fieldName)) {
            this.updateRowAmounts(row);
            await this.updateTotalAmounts();
        }

        // 商品コード特有の更新
        if (fieldName === 'product_code') {
            // マスタ有無のマーク更新
            const masterMarkCell = row.querySelector('td:nth-child(2)');
            if (masterMarkCell) {
                masterMarkCell.innerHTML = result.has_item ? 
                    '<span class="text-success">○</span>' : 
                    '<span class="text-danger">✘</span>';
            }

            // 商品名の更新
            const productNameCell = row.querySelector('td:nth-child(4)');
            if (productNameCell) {
                productNameCell.textContent = result.product_name || '';
            }

            // 課コード/クラスコードの更新
            const deptClassCodeCell = row.querySelector('.department-class-code');
            if (deptClassCodeCell) {
                const deptCode = result.department_code || '';
                const classCode = result.class_code || '';
                
                if (deptCode && classCode) {
                    deptClassCodeCell.textContent = `${deptCode}/${classCode}`;
                } else if (deptCode) {
                    deptClassCodeCell.textContent = `${deptCode}/`;
                } else if (classCode) {
                    deptClassCodeCell.textContent = `/${classCode}`;
                } else {
                    deptClassCodeCell.textContent = '';
                }
            }

            // マスタ情報の更新（マスタに存在する場合のみ）
            if (result.item) {
                // マスタ原単価 (7列目)
                const masterUnitCostCell = row.querySelector('td:nth-child(7)');
                if (masterUnitCostCell) {
                    if (result.item && result.item.unit_cost !== undefined) {
                        masterUnitCostCell.textContent = new Intl.NumberFormat('ja-JP', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        }).format(result.item.unit_cost);
                    } else {
                        masterUnitCostCell.textContent = '';
                    }
                }

                // マスタ売単価 (10列目)
                const masterUnitPriceCell = row.querySelector('td:nth-child(10)');
                if (masterUnitPriceCell) {
                    if (result.item && result.item.unit_price !== undefined) {
                        masterUnitPriceCell.textContent = new Intl.NumberFormat('ja-JP').format(result.item.unit_price);
                    } else {
                        masterUnitPriceCell.textContent = '';
                    }
                }
            } else {
                // マスタに存在しない場合は空にする
                row.querySelector('td:nth-child(7)').textContent = '';
                row.querySelector('td:nth-child(10)').textContent = '';
            }
        }

        // 履歴更新
        await updateHistoryList(slipId);
    },

    // 関連フィールドの更新
    updateRelatedFields(row, relatedFields) {
        Object.entries(relatedFields).forEach(([field, value]) => {
            const element = row.querySelector(`[name="${field}"]`);
            if (element) element.value = value;
        });
    },

    // 行の金額を更新
    updateRowAmounts(row) {
        const quantity = parseFloat(row.querySelector('input[name="quantity"]').value) || 0;
        const unitCost = parseFloat(row.querySelector('input[name="unit_cost"]').value) || 0;
        const unitPrice = parseFloat(row.querySelector('input[name="unit_price"]').value) || 0;

        // 原価金額と売価金額を計算（小数点以下を四捨五入）
        const costAmount = Math.round(quantity * unitCost);
        const sellingAmount = Math.round(quantity * unitPrice);

        // 金額表示を更新（整数値で表示）
        row.querySelector('.cost-amount').textContent = costAmount.toLocaleString();
        row.querySelector('.selling-amount').textContent = sellingAmount.toLocaleString();
    },

    // 合計金額を更新
    async updateTotalAmounts() {
        try {
            const slipId = document.querySelector('.slip-detail-form').dataset.slipId;
            const response = await fetch(`/slip/${slipId}/update-totals`, {
                method: 'POST',
                ...fetchConfig
            });

            const data = await response.json();
            if (data.status === 'success') {
                // 合計金額表示を更新（整数値で表示）
                document.getElementById('total-cost-amount').textContent = 
                    `原価合計金額: ${Math.round(data.cost_total).toLocaleString()} 円`;
                document.getElementById('total-selling-amount').textContent = 
                    `売価合計金額: ${Math.round(data.selling_total).toLocaleString()} 円`;

                // ヘッダー部分の金額表示も更新（整数値で表示）
                const costAmountHeader = document.querySelector('.amount-section:first-child .total-amount');
                const sellingAmountHeader = document.querySelector('.amount-section:last-child .total-amount');
                
                if (costAmountHeader) {
                    costAmountHeader.textContent = `${Math.round(data.cost_total).toLocaleString()} 円`;
                }
                if (sellingAmountHeader) {
                    sellingAmountHeader.textContent = `${Math.round(data.selling_total).toLocaleString()} 円`;
                }
            }
        } catch (error) {
            console.error('合計金額の更新に失敗しました:', error);
        }
    },

    // 商品コードの更新処理
    async updateProductCode(slipId, detailId, productCode, lineNumber, oldValue) {
        try {
            const response = await fetch(`/slip/${slipId}/details/${detailId}/product`, {
                ...fetchConfig,
                method: 'PUT',
                body: JSON.stringify({
                    product_code: productCode,
                    line_number: lineNumber,
                    old_value: oldValue
                })
            });
            const result = await response.json();
            
            if (response.ok && result.status === 'success') {
                const row = document.querySelector(`.slip-detail-row[data-detail-id="${detailId}"]`);
                if (row) {
                    // マスタ有無のマーク更新
                    const masterMarkCell = row.querySelector('td:nth-child(2)');
                    if (masterMarkCell) {
                        masterMarkCell.innerHTML = result.has_item ? 
                            '<span class="text-success">○</span>' : 
                            '<span class="text-danger">✘</span>';
                    }

                    // 商品名の更新
                    const productNameCell = row.querySelector('td:nth-child(4)');
                    if (productNameCell) {
                        productNameCell.textContent = result.product_name || '';
                    }

                    // マスタ原単価を表示（小数点以下2桁でフォーマット）
                    const masterUnitCostCell = row.querySelector('td:nth-child(7)');
                    if (masterUnitCostCell) {
                        if (result.item && result.item.unit_cost !== undefined) {
                            masterUnitCostCell.textContent = new Intl.NumberFormat('ja-JP', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            }).format(result.item.unit_cost);
                        } else {
                            masterUnitCostCell.textContent = '';
                        }
                    }

                    // マスタ売単価を表示（整数でフォーマット）
                    const masterUnitPriceCell = row.querySelector('td:nth-child(10)');
                    if (masterUnitPriceCell) {
                        if (result.item && result.item.unit_price !== undefined) {
                            masterUnitPriceCell.textContent = new Intl.NumberFormat('ja-JP').format(result.item.unit_price);
                        } else {
                            masterUnitPriceCell.textContent = '';
                        }
                    }

                    // 課コード/クラスコードの更新
                    const deptClassCodeCell = row.querySelector('.department-class-code');
                    if (deptClassCodeCell) {
                        const deptCode = result.department_code || '';
                        const classCode = result.class_code || '';
                        
                        if (deptCode && classCode) {
                            deptClassCodeCell.textContent = `${deptCode}/${classCode}`;
                        } else if (deptCode) {
                            deptClassCodeCell.textContent = `${deptCode}/`;
                        } else if (classCode) {
                            deptClassCodeCell.textContent = `/${classCode}`;
                        } else {
                            deptClassCodeCell.textContent = '';
                        }
                    }

                    // マスタに存在しない場合は空にする
                    if (!result.has_item) {
                        masterUnitCostCell.textContent = '';
                        masterUnitPriceCell.textContent = '';
                    }
                }
                return true;
            } else {
                throw new Error(result.error || '更新に失敗しました');
            }
        } catch (error) {
            console.error('商品コード更新エラー:', error);
            throw error;
        }
    },

    // 数量の更新処理
    async updateQuantity(slipId, detailId, quantity) {
        try {
            const response = await fetch(`/slip/${slipId}/details/${detailId}/quantity`, {
                ...fetchConfig,
                method: 'PUT',
                body: JSON.stringify({ quantity: Number(quantity) })
            });

            const result = await handleResponse(response);
            if (!result) return null;

            // 金額の再計算
            const row = document.querySelector(`.slip-detail-row[data-detail-id="${detailId}"]`);
            if (row) {
                this.updateRowAmounts(row);
                await this.updateTotalAmounts();
            }

            return result;
        } catch (error) {
            console.error('数量更新エラー:', error);
            throw error;
        }
    },

    // 原単価の更新処理
    async updateUnitCost(slipId, detailId, unitCost) {
        try {
            const response = await fetch(`/slip/${slipId}/details/${detailId}/unit-cost`, {
                ...fetchConfig,
                method: 'PUT',
                body: JSON.stringify({ unit_cost: Number(unitCost) })
            });

            const result = await handleResponse(response);
            if (result?.status === 'success') {
                // 履歴一覧を更新
                await updateHistoryList(slipId);
            }
            return result;
        } catch (error) {
            console.error('原単価更新エラー:', error);
            throw error;
        }
    },

    // 売単価の更新処理
    async updateUnitPrice(slipId, detailId, unitPrice) {
        try {
            const response = await fetch(`/slip/${slipId}/details/${detailId}/unit-price`, {
                ...fetchConfig,
                method: 'PUT',
                body: JSON.stringify({ unit_price: Math.round(Number(unitPrice)) })
            });

            const result = await handleResponse(response);
            if (result?.status === 'success') {
                // 入力値を整数に丸める
                const input = document.querySelector(`.slip-detail-row[data-detail-id="${detailId}"] input[name="unit_price"]`);
                if (input) {
                    input.value = Math.round(result.data.unit_price);
                }
                // 履歴一覧を更新
                await updateHistoryList(slipId);
            }
            return result;
        } catch (error) {
            console.error('売単価更新エラー:', error);
            throw error;
        }
    }
};

// DOMContentLoadedイベントでの初期化
document.addEventListener('DOMContentLoaded', () => {
    DetailRowManager.initialize();
});
