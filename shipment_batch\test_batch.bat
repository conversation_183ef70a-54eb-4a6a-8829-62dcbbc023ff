@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Basic Test Batch Start
echo ========================================

echo Current Directory: %cd%
echo Batch Directory: %~dp0

set BATCH_DIR=%~dp0
cd /d "%BATCH_DIR%"
echo Working Directory: %cd%

echo ========================================
echo Basic Test execution start: %date% %time%
echo ========================================

echo Executing: python test_basic.py
python test_basic.py
set TEST_RESULT=%errorlevel%

echo ========================================
echo Basic Test execution end: %date% %time%
echo Exit code: %TEST_RESULT%
echo ========================================

if %TEST_RESULT% equ 0 (
    echo Basic test completed successfully
    echo.
    echo Now you can run the main batch with: run_batch.bat
) else (
    echo Basic test failed ^(Exit code: %TEST_RESULT%^)
    echo.
    echo Please fix the issues before running the main batch
)

echo ========================================
echo Press any key to continue...
pause
exit /b %TEST_RESULT% 