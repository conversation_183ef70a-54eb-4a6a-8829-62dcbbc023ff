#!/usr/bin/env python3
"""
基本動作確認用テストスクリプト
"""
import sys
import os
from datetime import datetime

print("=" * 50)
print("基本動作確認テスト開始")
print(f"開始時刻: {datetime.now()}")
print("=" * 50)

try:
    # 作業ディレクトリの確認
    print(f"作業ディレクトリ: {os.getcwd()}")
    print(f"スクリプトディレクトリ: {os.path.dirname(os.path.abspath(__file__))}")
    
    # Pythonバージョン確認
    print(f"Pythonバージョン: {sys.version}")
    
    # 必要なモジュールのインポートテスト
    print("\nモジュールインポートテスト:")
    
    print("- logging...", end="")
    import logging
    print("OK")
    
    print("- configparser...", end="")
    import configparser
    print("OK")
    
    print("- datetime...", end="")
    from datetime import datetime
    print("OK")
    
    # 設定ファイルの存在確認
    print("\n設定ファイル確認:")
    config_path = os.path.join('config', 'config.ini')
    print(f"- {config_path}...", end="")
    if os.path.exists(config_path):
        print("OK")
        
        # 設定ファイルの読み込みテスト
        print("- 設定ファイル読み込み...", end="")
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        print("OK")
        
        # セクション確認
        print("- セクション確認:")
        for section in config.sections():
            print(f"  - {section}: OK")
    else:
        print("NG - ファイルが見つかりません")
    
    # ディレクトリ作成テスト
    print("\nディレクトリ作成テスト:")
    test_dirs = ['logs', 'output', 'backup']
    for dir_name in test_dirs:
        print(f"- {dir_name}...", end="")
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print("作成")
        else:
            print("存在")
    
    # ログファイル作成テスト
    print("\nログファイル作成テスト:")
    log_file = os.path.join('logs', 'test.log')
    print(f"- {log_file}...", end="")
    try:
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"テストログ: {datetime.now()}\n")
        print("OK")
    except Exception as e:
        print(f"NG - {e}")
    
    # srcディレクトリのファイル確認
    print("\nsrcディレクトリファイル確認:")
    src_files = [
        'src/shipment_batch.py',
        'src/utils.py',
        'src/database.py',
        'src/file_generator.py',
        'src/models.py'
    ]
    
    for file_path in src_files:
        print(f"- {file_path}...", end="")
        if os.path.exists(file_path):
            print("OK")
        else:
            print("NG - ファイルが見つかりません")
    
    print("\n=" * 50)
    print("基本動作確認テスト完了: 成功")
    print("=" * 50)
    
except Exception as e:
    print(f"\n基本動作確認テストエラー: {e}")
    print(f"エラータイプ: {type(e).__name__}")
    
    import traceback
    print("スタックトレース:")
    traceback.print_exc()
    
    print("\n=" * 50)
    print("基本動作確認テスト完了: 失敗")
    print("=" * 50)
    
    sys.exit(1) 