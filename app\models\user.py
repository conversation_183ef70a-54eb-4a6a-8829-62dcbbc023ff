from flask_login import UserMixin
from app import db
from werkzeug.security import check_password_hash

class User(UserMixin, db.Model):
    __bind_key__ = 'OCRInvoice'
    __tablename__ = 'users'
    __table_args__ = {
        'schema': 'dbo',
        'extend_existing': True
    }

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.Unicode(50), nullable=False)
    password_hash = db.Column(db.Unicode(255), nullable=True)
    is_approver = db.Column(db.<PERSON><PERSON><PERSON>, nullable=True)
    is_deleted = db.Column(db.Integer, nullable=False, default=0)
    adminuser = db.Column(db.<PERSON>, nullable=False, default=False)
    def __repr__(self):
        return f"<User {self.username}>"
        
    def check_password(self, password):
        if self.is_deleted != 0:
            return False
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        """ユーザーが管理者かどうかをチェック"""
        return bool(self.adminuser)

    @staticmethod
    def can_delete_users(user):
        """ユーザーが他のユーザーを削除できるかチェック"""
        return user and user.is_admin()
