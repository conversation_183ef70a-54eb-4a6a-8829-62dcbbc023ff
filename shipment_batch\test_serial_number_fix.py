#!/usr/bin/env python3
"""
フッターレコードのデータシリアルNo修正テスト
"""
import sys
import os
from datetime import datetime

# srcディレクトリをパスに追加
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from models import PurchaseSlip, PurchaseSlipDetail
    from utils import ConfigManager
    from file_generator import FileGenerator
    
    print("=" * 60)
    print("フッターレコードのデータシリアルNo修正テスト")
    print(f"開始時刻: {datetime.now()}")
    print("=" * 60)
    
    # 設定管理の初期化
    config = ConfigManager('config/config.ini')
    
    # ファイル生成器の初期化
    generator = FileGenerator(config)
    
    # テスト用の伝票データを作成
    from decimal import Decimal
    
    slip1 = PurchaseSlip(
        id=1,
        slip_number="12345",
        slip_date=datetime.now(),
        company_code="01",
        vendor_code="1234",
        vendor_name="テスト取引先",
        store_code="01",
        store_name="テスト店舗",
        store_name_kana="テストテンポ",
        department_code="100",
        cost_amount=Decimal('1000'),
        selling_amount=Decimal('1200'),
        tax_division_code="01",
        tax_rate=Decimal('10'),
        status="approved",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        send_flg=False,
        approved_at=None,
        approved_by=None,
        err_description=None,
        is_deleted=0,
        send_datetime=None
    )
    
    # 明細データを作成（複数の明細でテスト）
    detail1 = PurchaseSlipDetail(
        id=1,
        slip_id=1,
        line_number=1,
        product_code="PROD001",
        product_name="テスト商品1",
        unit="個",
        quantity=Decimal('2'),
        unit_cost=Decimal('250'),
        unit_price=Decimal('300'),
        amount=Decimal('500'),
        item_id=None,
        section_id=None,
        org_product_code=None,
        org_product_name=None,
        org_unit=None,
        org_quantity=None,
        org_unit_cost=None,
        org_unit_price=None,
        org_amount=None
    )
    
    detail2 = PurchaseSlipDetail(
        id=2,
        slip_id=1,
        line_number=2,
        product_code="PROD002", 
        product_name="テスト商品2",
        unit="個",
        quantity=Decimal('1'),
        unit_cost=Decimal('500'),
        unit_price=Decimal('600'),
        amount=Decimal('500'),
        item_id=None,
        section_id=None,
        org_product_code=None,
        org_product_name=None,
        org_unit=None,
        org_quantity=None,
        org_unit_cost=None,
        org_unit_price=None,
        org_amount=None
    )
    
    slip1.details = [detail1, detail2]
    
    # ファイル生成
    print("ファイル生成開始...")
    output_file = generator.generate_shipment_file([slip1])
    print(f"ファイル生成完了: {output_file}")
    
    # 生成されたファイルの内容を確認
    print("\n生成されたファイルの内容確認:")
    with open(output_file, 'r', encoding='shift_jis', errors='replace') as f:
        lines = f.readlines()
    
    print(f"総行数: {len(lines)}")
    
    # 各レコードのデータシリアルNoを抽出して確認
    serial_numbers = []
    trailer_info = None
    nnn4_count = 0  # NNN4レコードの実際の数をカウント
    
    for i, line in enumerate(lines):
        line_content = line.rstrip('\n\r')
        
        # 256バイト行の前半128バイト（0-127）をチェック
        if len(line_content) >= 128:
            first_half = line_content[:128]
            if first_half.startswith(('NNN0', 'NNN1', 'NNN4', 'NNN9')):
                serial_no = first_half[123:128]  # 123-127桁目（5桁）
                record_type = first_half[:4]
                serial_numbers.append((i+1, '前半', record_type, serial_no))
                print(f"行{i+1} 前半: {record_type} - シリアルNo: {serial_no}")
                
                # NNN4レコードをカウント
                if record_type == 'NNN4':
                    nnn4_count += 1
                
                # NNN9レコードの場合、配信レコード件数と配信アイテム件数も取得
                if record_type == 'NNN9':
                    delivery_record_count = first_half[24:30]  # 24-29桁目（6桁）
                    delivery_item_count = first_half[30:36]    # 30-35桁目（6桁）
                    trailer_info = {
                        'serial_no': serial_no,
                        'delivery_record_count': delivery_record_count,
                        'delivery_item_count': delivery_item_count,
                        'position': f"行{i+1} 前半"
                    }
                    print(f"  配信レコード件数: {delivery_record_count}")
                    print(f"  配信アイテム件数: {delivery_item_count}")
        
        # 256バイト行の後半128バイト（128-255）をチェック
        if len(line_content) >= 256:
            second_half = line_content[128:256]
            if second_half.startswith(('NNN0', 'NNN1', 'NNN4', 'NNN9')):
                serial_no = second_half[123:128]  # 123-127桁目（5桁）
                record_type = second_half[:4]
                serial_numbers.append((i+1, '後半', record_type, serial_no))
                print(f"行{i+1} 後半: {record_type} - シリアルNo: {serial_no}")
                
                # NNN4レコードをカウント
                if record_type == 'NNN4':
                    nnn4_count += 1
                
                # NNN9レコードの場合、配信レコード件数と配信アイテム件数も取得
                if record_type == 'NNN9':
                    delivery_record_count = second_half[24:30]  # 24-29桁目（6桁）
                    delivery_item_count = second_half[30:36]    # 30-35桁目（6桁）
                    trailer_info = {
                        'serial_no': serial_no,
                        'delivery_record_count': delivery_record_count,
                        'delivery_item_count': delivery_item_count,
                        'position': f"行{i+1} 後半"
                    }
                    print(f"  配信レコード件数: {delivery_record_count}")
                    print(f"  配信アイテム件数: {delivery_item_count}")
    
    # 最後のNNN4レコードとNNN9レコードのシリアル番号を比較
    print("\nシリアル番号の検証:")
    last_nnn4_serial = None
    nnn9_serial = None
    
    for line_no, position, record_type, serial_no in serial_numbers:
        if record_type == 'NNN4':
            last_nnn4_serial = serial_no
            print(f"最後のNNN4レコード: 行{line_no} {position} - シリアルNo: {serial_no}")
        elif record_type == 'NNN9':
            nnn9_serial = serial_no
            print(f"NNN9レコード: 行{line_no} {position} - シリアルNo: {serial_no}")
    
    # 検証結果
    print("\n検証結果:")
    
    # 1. フッターレコードのシリアルNo検証
    if last_nnn4_serial and nnn9_serial:
        # フッターレコードのシリアルNoは、最後のNNN4レコードのシリアルNo + 1 であることを確認
        expected_nnn9_serial = f"{int(last_nnn4_serial) + 1:05d}"
        if nnn9_serial == expected_nnn9_serial:
            print("✓ 成功: フッターレコードのシリアルNoが最後のNNN4レコード + 1です")
            print(f"  最後のNNN4: {last_nnn4_serial}")
            print(f"  NNN9: {nnn9_serial}")
            print(f"  期待値: {expected_nnn9_serial}")
        else:
            print("✗ 失敗: フッターレコードのシリアルNoが期待値と異なります")
            print(f"  最後のNNN4: {last_nnn4_serial}")
            print(f"  NNN9: {nnn9_serial}")
            print(f"  期待値: {expected_nnn9_serial}")
    else:
        print("✗ エラー: NNN4またはNNN9レコードが見つかりません")
    
    # 2. 配信レコード件数とシリアルNoの一致検証
    if trailer_info:
        print(f"\n配信レコード件数の検証:")
        print(f"  フッターレコード位置: {trailer_info['position']}")
        print(f"  データシリアルNo: {trailer_info['serial_no']}")
        print(f"  配信レコード件数: {trailer_info['delivery_record_count']}")
        
        # シリアルNoを6桁にゼロパディングして比較
        expected_delivery_count = f"{int(trailer_info['serial_no']):06d}"
        if trailer_info['delivery_record_count'] == expected_delivery_count:
            print("✓ 成功: 配信レコード件数がフッターのシリアルNoと一致しています")
        else:
            print("✗ 失敗: 配信レコード件数がフッターのシリアルNoと一致しません")
            print(f"  期待値: {expected_delivery_count}")
        
        # 3. 配信アイテム件数とNNN4レコード数の一致検証
        print(f"\n配信アイテム件数の検証:")
        print(f"  配信アイテム件数: {trailer_info['delivery_item_count']}")
        print(f"  実際のNNN4レコード数: {nnn4_count}")
        
        expected_item_count = f"{nnn4_count:06d}"
        if trailer_info['delivery_item_count'] == expected_item_count:
            print("✓ 成功: 配信アイテム件数が実際のNNN4レコード数と一致しています")
        else:
            print("✗ 失敗: 配信アイテム件数が実際のNNN4レコード数と一致しません")
            print(f"  期待値: {expected_item_count}")
    else:
        print("\n✗ エラー: フッターレコード情報が取得できませんでした")
    
    print("\n=" * 60)
    print("テスト完了")
    print("=" * 60)
    
except Exception as e:
    print(f"\nテストエラー: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1) 