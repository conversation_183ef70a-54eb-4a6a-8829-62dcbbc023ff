@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Shipment Batch Start
echo ========================================

echo Current Directory: %cd%
echo Batch Directory: %~dp0

set BATCH_DIR=%~dp0
cd /d "%BATCH_DIR%"
echo Working Directory: %cd%

echo ========================================
echo Environment Check
echo ========================================

echo Checking Python environment...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python not found.
    pause
    exit /b 1
)

echo Checking Python path...
python -c "import sys; print('Python executable:', sys.executable)"
python -c "import sys; print('Python path:', sys.path)"

echo ========================================
echo File Check
echo ========================================

if exist src\shipment_batch.py (
    echo OK: src\shipment_batch.py found
    dir src\shipment_batch.py
) else (
    echo ERROR: src\shipment_batch.py not found
    pause
    exit /b 1
)

if exist config\config.ini (
    echo OK: config\config.ini found
    dir config\config.ini
) else (
    echo ERROR: config\config.ini not found
    pause
    exit /b 1
)

echo ========================================
echo Directory Setup
echo ========================================

if not exist logs (
    echo Creating logs directory...
    mkdir logs
)
if not exist output (
    echo Creating output directory...
    mkdir output
)
if not exist backup (
    echo Creating backup directory...
    mkdir backup
)

echo Directory structure:
dir /b

echo ========================================
echo Python Module Check
echo ========================================

echo Checking required modules...
python -c "import logging; print('logging: OK')" 2>&1
python -c "import sys; print('sys: OK')" 2>&1
python -c "import os; print('os: OK')" 2>&1
python -c "import configparser; print('configparser: OK')" 2>&1

echo ========================================
echo Batch execution start: %date% %time%
echo ========================================

echo Executing: python src\shipment_batch.py
echo Command line: python src\shipment_batch.py

python src\shipment_batch.py
set BATCH_RESULT=%errorlevel%

echo ========================================
echo Batch execution end: %date% %time%
echo Exit code: %BATCH_RESULT%
echo ========================================

echo Checking output files...
if exist logs\*.log (
    echo Log files found:
    dir logs\*.log
    echo.
    echo Latest log content:
    echo ----------------------------------------
    type logs\shipment_batch.log 2>nul
    echo ----------------------------------------
) else (
    echo No log files found in logs directory
)

if exist output\*.* (
    echo Output files found:
    dir output\*.*
) else (
    echo No output files found in output directory
)

if %BATCH_RESULT% equ 0 (
    echo Shipment batch completed successfully
) else (
    echo Shipment batch failed ^(Exit code: %BATCH_RESULT%^)
    echo.
    echo Troubleshooting information:
    echo - Check if database connection is available
    echo - Check if required Python modules are installed
    echo - Check config.ini settings
    echo - Check log files for detailed error information
)

echo ========================================
echo Press any key to continue...
pause
exit /b %BATCH_RESULT% 