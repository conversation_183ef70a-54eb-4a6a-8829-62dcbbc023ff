#!/usr/bin/env python3
"""
section_code取得テスト用スクリプト
"""
import sys
import os

# プロジェクトルートをパスに追加
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils import ConfigManager
from database import DatabaseManager

def test_section_code_retrieval():
    """section_codeの取得をテストする"""
    print("=== section_code取得テスト開始 ===")
    
    try:
        # 設定管理初期化
        config = ConfigManager()
        
        # データベース管理初期化
        db_manager = DatabaseManager(config)
        db_manager.connect()
        
        # テスト用のsection_idを取得
        print("テスト用のsection_idを取得中...")
        if db_manager.inventory_connection:
            cursor = db_manager.inventory_connection.cursor()
            
            # サンプルのsection_idを取得
            cursor.execute("""
                SELECT TOP 5 Id, SectionCode, SectionName
                FROM dbo.Sections
                WHERE DelFlg = 0 AND SectionCode IS NOT NULL
                ORDER BY Id
            """)
            
            test_sections = cursor.fetchall()
            
            print("テスト対象のセクション:")
            for section in test_sections:
                print(f"  ID: {section.Id}, Code: {section.SectionCode}, Name: {section.SectionName}")
            
            print("\nget_section_codeメソッドのテスト:")
            for section in test_sections:
                section_id = section.Id
                expected_code = section.SectionCode
                
                # get_section_codeメソッドを呼び出し
                retrieved_code = db_manager.get_section_code(section_id)
                
                if retrieved_code == expected_code:
                    print(f"  ✅ ID {section_id}: 期待値 '{expected_code}' = 取得値 '{retrieved_code}'")
                else:
                    print(f"  ❌ ID {section_id}: 期待値 '{expected_code}' ≠ 取得値 '{retrieved_code}'")
            
            # 存在しないIDのテスト
            print("\n存在しないIDのテスト:")
            non_existent_id = 999999
            result = db_manager.get_section_code(non_existent_id)
            if result is None:
                print(f"  ✅ 存在しないID {non_existent_id}: 正しくNoneが返されました")
            else:
                print(f"  ❌ 存在しないID {non_existent_id}: 予期しない値 '{result}' が返されました")
            
            # NoneのIDのテスト
            print("\nNoneのIDのテスト:")
            result = db_manager.get_section_code(None)
            if result is None:
                print(f"  ✅ None ID: 正しくNoneが返されました")
            else:
                print(f"  ❌ None ID: 予期しない値 '{result}' が返されました")
        
        print("=== section_code取得テスト完了 ===")
        
    except Exception as e:
        print(f"テスト中にエラーが発生: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if 'db_manager' in locals():
            db_manager.disconnect()

if __name__ == "__main__":
    test_section_code_retrieval() 