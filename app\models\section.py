from app import db
from sqlalchemy import BigInteger, String, Integer, DateTime, Boolean, Numeric

class Section(db.Model):
    __bind_key__ = 'YszInventory'
    __tablename__ = 'Sections'
    
    id = db.Column('Id', BigInteger, primary_key=True)
    code_level = db.Column('CodeLevel', Integer, nullable=False, default=0)
    unique_code = db.Column('UniqueCode', String(9), nullable=False, default='')
    section_code = db.Column('SectionCode', String(6), nullable=False, default='')
    section_name = db.Column('SectionName', String(50), nullable=False, default='')
    parent_code_id = db.Column('ParentCodeId', BigInteger, 
                              db.ForeignKey('Sections.Id'), 
                              nullable=True, default=0)
    control_type = db.Column('ControlType', Integer, nullable=False, default=0)
    default_cost_rate = db.Column('DefaultCostRate', Numeric(5,0), nullable=False, default=0)
    store_group_code = db.Column('StoreGroupCode', String(50), nullable=True)
    clothing_flg = db.Column('ClothingFlg', Boolean, nullable=False, default=False)
    del_flg = db.Column('DelFlg', BigInteger, nullable=False, default=0)
    cr_date_time = db.Column('CrDateTime', DateTime, nullable=False, 
                            server_default=db.text('getdate()'))
    up_date_time = db.Column('UpDateTime', DateTime, nullable=False, 
                            server_default=db.text('getdate()'))
    fresh_edi_line_code = db.Column('FreshEDILineCode', String(4), nullable=True)
    
    def __repr__(self):
        return f"<Section {self.section_code}: {self.section_name}>"
