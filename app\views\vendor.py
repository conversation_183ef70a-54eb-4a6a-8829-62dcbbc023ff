from flask import Blueprint, render_template, request, jsonify, current_app, flash, redirect, url_for
from flask_login import login_required, current_user
from app import db
from app.models import Vendor, PurchaseSlip
from decimal import Decimal, InvalidOperation

vendor = Blueprint('vendor', __name__, url_prefix='/vendor')

@vendor.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    if not current_user.is_approver:
        flash('この画面にアクセスする権限がありません。')
        return redirect(url_for('auth.index'))

    if request.method == 'POST':
        try:
            if request.is_json:
                data = request.get_json()
            else:
                data = request.form

            vendor_code = data.get('vendor_code')
            vendor_name = data.get('vendor_name')
            tax_type_code = data.get('tax_type', '01')  # デフォルトは原売価内税
            tax_rate = data.get('tax_rate', '10.0')  # デフォルトは10.0%

            if not vendor_code or not vendor_name:
                return jsonify({'error': '必須項目が入力されていません'}), 400

            if not vendor_code.isdigit() or len(vendor_code) != 4:
                return jsonify({'error': '取引先コードは4桁の数字で入力してください'}), 400

            # 税区分のバリデーション（新しい3つのコード）
            valid_tax_codes = ['01', '03', '05']
            if tax_type_code not in valid_tax_codes:
                return jsonify({'error': '税区分は01、03、05のいずれかを選択してください'}), 400

            # 消費税率のバリデーション
            try:
                tax_rate_decimal = Decimal(str(tax_rate))
                if tax_rate_decimal < 0 or tax_rate_decimal > 99.9:
                    return jsonify({'error': '消費税率は0.0～99.9の範囲で入力してください'}), 400
            except (InvalidOperation, ValueError):
                return jsonify({'error': '消費税率は数値で入力してください'}), 400

            if Vendor.query.filter_by(vendor_code=vendor_code).first():
                return jsonify({'error': 'この取引先コードは既に登録されています'}), 400

            # 新しいVendorインスタンスを作成
            vendor = Vendor(
                vendor_code=vendor_code, 
                vendor_name=vendor_name,
                tax_rate=tax_rate_decimal
            )
            # プロパティを使用して税区分を設定（2桁コード→整数値に変換）
            vendor.tax_type_code = tax_type_code
            
            db.session.add(vendor)
            db.session.commit()
            
            return jsonify({'message': '取引先を登録しました', 'status': 'success'})

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"取引先登録エラー: {str(e)}")
            return jsonify({'error': '取引先の登録に失敗しました'}), 500

    vendors = Vendor.query.order_by(Vendor.vendor_code).all()
    return render_template('vendor_settings.html', vendors=vendors)

@vendor.route('/settings/<int:vendor_id>', methods=['PUT'])
@login_required
def update_vendor(vendor_id):
    if not current_user.is_approver:
        return jsonify({'error': '承認権限が必要です'}), 403
    try:
        vendor = Vendor.query.get_or_404(vendor_id)
        data = request.get_json()
        
        # 取引先名の更新
        if 'vendor_name' in data:
            vendor.vendor_name = data['vendor_name']
        
        # 税区分の更新
        if 'tax_type' in data:
            valid_tax_codes = ['01', '03', '05']
            if data['tax_type'] not in valid_tax_codes:
                return jsonify({'error': '税区分は01、03、05のいずれかを選択してください'}), 400
            # プロパティを使用して税区分を設定
            vendor.tax_type_code = data['tax_type']
        
        # 消費税率の更新
        if 'tax_rate' in data:
            try:
                tax_rate_decimal = Decimal(str(data['tax_rate']))
                if tax_rate_decimal < 0 or tax_rate_decimal > 99.9:
                    return jsonify({'error': '消費税率は0.0～99.9の範囲で入力してください'}), 400
                vendor.tax_rate = tax_rate_decimal
            except (InvalidOperation, ValueError):
                return jsonify({'error': '消費税率は数値で入力してください'}), 400
            
        db.session.commit()
        return jsonify({'message': '取引先情報を更新しました'})
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"取引先更新エラー: {str(e)}")
        return jsonify({'error': '取引先の更新に失敗しました'}), 500

@vendor.route('/<int:vendor_id>', methods=['DELETE'])
@login_required
def delete_vendor(vendor_id):
    if not current_user.is_approver:
        return jsonify({'error': '承認権限が必要です'}), 403
    try:
        vendor = Vendor.query.get_or_404(vendor_id)
        
        # 関連する仕入伝票の確認（日付の新しい順に最大5件）
        related_slips = PurchaseSlip.query\
            .filter_by(vendor_code=vendor.vendor_code)\
            .order_by(PurchaseSlip.slip_date.desc())\
            .limit(5)\
            .all()

        # 全件数の取得
        total_slips = PurchaseSlip.query\
            .filter_by(vendor_code=vendor.vendor_code)\
            .count()
            
        if total_slips > 0:
            slip_details = [{
                'slip_number': slip.slip_number,
                'slip_date': slip.slip_date.strftime('%Y-%m-%d'),
                'status': '承認済' if slip.status == 'approved' else '未承認'
            } for slip in related_slips]
            
            return jsonify({
                'error': '関連する仕入伝票が存在するため削除できません',
                'details': {
                    'related_slips': slip_details,
                    'total_slips': total_slips,
                    'showing_slips': len(related_slips)
                }
            }), 400
            
        db.session.delete(vendor)
        db.session.commit()
        return jsonify({'message': '取引先を削除しました'})
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"取引先削除エラー: {str(e)}")
        return jsonify({'error': '取引先の削除に失敗しました'}), 500

