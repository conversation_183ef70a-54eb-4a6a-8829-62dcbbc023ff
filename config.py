import os
from datetime import timedelta

class Config:
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key'

    # データベース接続設定
    SQLALCHEMY_BINDS = {
        'YszInventory': os.environ.get('YSZ_INVENTORY_DB') or 'mssql+pyodbc:///?odbc_connect=Driver={SQL Server};Server=H360C;Database=YszInventory;UID=sa;PWD=*********',
        'OCRInvoice': os.environ.get('OCR_INVOICE_DB') or 'mssql+pyodbc:///?odbc_connect=Driver={SQL Server};Server=H360C;Database=YszOCRInvoice;UID=sa;PWD=*********'
    }

    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # SQLAlchemy エンジン設定
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,                    # 接続プールサイズ
        'pool_timeout': 30,                 # 接続取得タイムアウト（秒）
        'pool_recycle': 3600,              # 接続の再利用時間（秒）
        'pool_pre_ping': True,             # 接続前のpingテスト
        'max_overflow': 20,                # プールサイズを超えた場合の最大接続数
        'connect_args': {
            'timeout': 30,                 # 接続タイムアウト
            'autocommit': False,           # 自動コミット無効
            'check_same_thread': False     # スレッドセーフ
        }
    }

    # セッション設定
    PERMANENT_SESSION_LIFETIME = timedelta(minutes=30)
    SESSION_COOKIE_SECURE = True  # HTTPS接続の場合
    SESSION_COOKIE_HTTPONLY = True
