import os
from datetime import timedelta

class Config:
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key'

    # データベース接続設定
    SQLALCHEMY_BINDS = {
        'YszInventory': os.environ.get('YSZ_INVENTORY_DB') or 'mssql+pyodbc:///?odbc_connect=Driver={SQL Server};Server=H360C;Database=YszInventory;UID=sa;PWD=*********',
        'OCRInvoice': os.environ.get('OCR_INVOICE_DB') or 'mssql+pyodbc:///?odbc_connect=Driver={SQL Server};Server=H360C;Database=YszOCRInvoice;UID=sa;PWD=*********'
    }

    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # セッション設定
    PERMANENT_SESSION_LIFETIME = timedelta(minutes=30)
    SESSION_COOKIE_SECURE = True  # HTTPS接続の場合
    SESSION_COOKIE_HTTPONLY = True
