/* slip_detail.html 専用のスタイル */

/* ヘッダー部分のスタイル */
.slip-detail-form .header-form-group {
    margin-bottom: 0;
}

/* 入力欄の幅調整 */
.slip-detail-form input[name="vendor_code"] {
    max-width: 90px;
}

.slip-detail-form input[name="slip_date"] {
    max-width: 140px;
}

.slip-detail-form input[name="department_code"] {
    max-width: 100px;
}

.slip-detail-form input[name="slip_number"] {
    max-width: 250px;
}

.slip-detail-form input[name="tax_type"],
.slip-detail-form input[name="tax_division_code"] {
    max-width: 60px;
}

.slip-detail-form input[name="company_code"],
.slip-detail-form input[name="store_code"] {
    max-width: 80px;
}

/* ステータスバッジの配置調整 */
.slip-detail-form .badge {
    margin-top: 8px;
    display: inline-block;
}

/* ヘッダーフォーム全体のマージン設定 */
.slip-detail-form .card-body .row.mb-3 {
    margin-left: 1rem;
    padding-right: 1rem;
}

/* テーブル内のスタイル */
.slip-detail-form .table th,
.slip-detail-form .table td {
    white-space: nowrap;
}

/* 明細テーブルのセル幅調整 */
.slip-detail-form .table th:nth-child(1) { width: 70px; }   /* 行番号 */
.slip-detail-form .table th:nth-child(2) { width: 50px; }   /* マスタ */
.slip-detail-form .table th:nth-child(3) { width: 160px; }  /* 商品コード */
.slip-detail-form .table th:nth-child(5) { width: 100px; }  /* 数量 */
.slip-detail-form .table th:nth-child(6) { width: 120px; }  /* マスタ原単価 */
.slip-detail-form .table th:nth-child(7) { width: 120px; }  /* 原単価 */
.slip-detail-form .table th:nth-child(8) { width: 120px; }  /* 原価金額 */
.slip-detail-form .table th:nth-child(9) { width: 120px; }  /* マスタ売単価 */
.slip-detail-form .table th:nth-child(10) { width: 120px; } /* 売単価 */
.slip-detail-form .table th:nth-child(11) { width: 120px; } /* 売価金額 */

/* マスタ列の中央揃え */
.slip-detail-form .table th:nth-child(2),
.slip-detail-form .table td:nth-child(2) {
    text-align: center;
}

/* マスタ価格列のスタイル */
.slip-detail-form .table td:nth-child(6),
.slip-detail-form .table td:nth-child(9) {
    background-color: var(--master-price-bg);
    font-style: italic;
    color: var(--master-price-text);
}

/* マスタ価格列のヘッダー */
.slip-detail-form .table th:nth-child(6),
.slip-detail-form .table th:nth-child(9) {
    background-color: var(--master-price-header-bg);
    font-style: italic;
    color: var(--master-price-header-text);
}

/* エラーボックスのスタイル */
.error-box {
    background-color: var(--error-box-bg);
    border: 1px solid var(--error-box-border);
    border-radius: 4px;
}

.error-box .card-body {
    padding: 1rem;
    color: var(--error-text-color);
}

.error-item {
    margin: 0.5rem 0;
    padding: 0.5rem;
    background-color: var(--error-item-bg);
    border-radius: 4px;
}

.error-item:first-child {
    margin-top: 0;
}

.error-item:last-child {
    margin-bottom: 0;
}

/* 送信日時の表示スタイル */
.send-datetime {
    position: relative;
    z-index: 10;
    padding: 0.5rem;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* テーマ別スタイル */
[data-bs-theme="light"] {
    /* 送信日時 - ライトモード */
    .send-datetime {
        background-color: rgba(182, 250, 217, 0.8); /* ライトブルー */
        color: var(--custom-text-color);
    }

    /* 売単価入力ボックス - ライトモード */
    .slip-detail-form .table input[name="unit_price"] {
        background-color: #fff;  /* 白背景 */
        color: #212529;         /* 暗めのテキスト */
        border-color: #ced4da;  /* Bootstrap標準のボーダー色 */
    }
}

[data-bs-theme="dark"] {
    /* 送信日時 - ダークモード */
    .send-datetime {
        background-color: rgba(44, 48, 52, 0.8);
        color: var(--custom-text-color);
    }

    /* 売単価入力ボックス - ダークモード */
    .slip-detail-form .table input[name="unit_price"] {
        background-color: #212529;  /* ダークモードの背景色 */
        color: #fff;               /* 白テキスト */
        border-color: #495057;     /* ダークモードのボーダー色 */
    }
}

/* 金額表示エリアのスタイル */
.amount-section {
    background-color: var(--custom-card-bg);
    padding: 1rem;
    min-width: 200px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.total-amount-label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--custom-text-color);
    opacity: 0.8;
}

.total-amount {
    font-size: 1.75rem;
    font-weight: bold;
    color: var(--total-amount-color);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
