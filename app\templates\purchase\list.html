<!-- 検索条件セクション -->
<div class="search-section">
    <div class="search-conditions">
        // ... existing code ...
    </div>
    <div class="search-buttons">
        <div class="button-group">
            // ... existing code ...
            <!-- インポートボタン -->
            <button type="button" id="importButton" class="btn btn-primary">
                <i class="fas fa-file-import"></i> インポート
            </button>
            <!-- 非表示のファイル入力 -->
            <input type="file" id="csvFileInput" accept=".csv" style="display: none;">
        </div>
    </div>
</div>

<!-- インポート処理中のオーバーレイ -->
<div id="importOverlay" class="overlay" style="display: none;">
    <div class="overlay-content">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">インポート中...</span>
        </div>
        <p>CSVファイルをインポート中...</p>
    </div>
</div>

<!-- CSS追加 -->
<style>
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.overlay-content {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
}
</style>

<!-- JavaScript追加 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const importButton = document.getElementById('importButton');
    const csvFileInput = document.getElementById('csvFileInput');
    const importOverlay = document.getElementById('importOverlay');

    // インポートボタンクリック時の処理
    importButton.addEventListener('click', function() {
        csvFileInput.click();
    });

    // ファイル選択時の処理
    csvFileInput.addEventListener('change', async function(e) {
        if (!e.target.files.length) return;

        const file = e.target.files[0];
        if (!file.name.toLowerCase().endsWith('.csv')) {
            alert('CSVファイルを選択してください。');
            return;
        }

        try {
            importOverlay.style.display = 'flex';
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/purchase/import', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            if (result.success) {
                alert('インポートが完了しました。');
                // 必要に応じて画面更新などの処理を追加
            } else {
                alert(`インポートに失敗しました。\n${result.message}`);
            }
        } catch (error) {
            alert('インポート処理でエラーが発生しました。');
            console.error('Import error:', error);
        } finally {
            importOverlay.style.display = 'none';
            csvFileInput.value = ''; // ファイル選択をリセット
        }
    });
});</script> 