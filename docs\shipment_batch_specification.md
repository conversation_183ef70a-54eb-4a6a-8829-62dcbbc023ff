# Purchase Slip Shipment Data Export Batch
## バッチ開発計画書・仕様書

---

## 📋 プロジェクト概要

### プロジェクト名
**Purchase Slip Shipment Data Export Batch**

### 目的
承認済み未送信の仕入伝票データを固定長ファイル形式で出力し、外部システムへの連携を行う

### 実行環境
- **OS**: Windows Server
- **言語**: Python 3.x
- **実行方式**: スケジューラー実行（単独実行）
- **文字コード**: Shift-JIS
- **データベース**: SQL Server (OCRInvoice)

---

## 🎯 機能要件

### 1. データ抽出条件
以下の条件に該当する仕入伝票データを抽出する：

```sql
SELECT ps.*, psd.*
FROM purchase_slip ps
INNER JOIN purchase_slip_detail psd ON ps.id = psd.slip_id
WHERE ps.is_deleted = 0          -- 論理削除されていない
  AND ps.send_flg = 0            -- 未送信
  AND ps.approved_by IS NOT NULL -- 承認済み
ORDER BY ps.id, psd.line_number
```

### 2. 出力ファイル仕様
- **ファイル名**: `Shipment_yyyymmddhhmiss.DAT`
- **文字コード**: Shift-JIS
- **レコード長**: 128バイト固定長
- **出力先**: 設定ファイルで指定されたパス
- **構造**: ファイルヘッダー + 伝票ヘッダー + 明細 + トレーラー

### 3. 処理後のデータ更新
出力完了後、対象レコードを以下のように更新：
- `send_flg = True`
- `send_datetime = getdate()`

---

## 📄 ファイルレイアウト仕様

### 🔷 ファイルヘッダー（1レコード/ファイル）

| 項目名 | 型 | 桁数 | オフセット | 寄せ | 内容分類 | セット内容/備考 |
|--------|----|----|----------|------|----------|----------------|
| データ種別 | 文字列 | 2 | 0 | L | 固定値 | `"NN"` |
| レコード種別 | 文字列 | 2 | 2 | L | 固定値 | `"N0"` |
| FILLER1 | 文字列 | 20 | 4 | L | 固定値 | 空白埋め（20桁） |
| ファイル作成日 | 文字列 | 8 | 24 | L | ルール生成 | システム日付 `"YYYYMMDD"` |
| ファイル作成時分秒 | 文字列 | 6 | 32 | L | ルール生成 | システム時刻 `"HHMISS"` |
| データ送信先 | 文字列 | 7 | 38 | L | データ参照 | vendor_code（0埋め） |
| データ送信元 | 文字列 | 6 | 45 | L | 固定値 | `"000001"` |
| レコード長 | 整数 | 3 | 51 | R | 固定値 | `128` |
| FILLER2 | 文字列 | 69 | 54 | L | 固定値 | 空白埋め（69桁） |
| データシリアルNo | 整数 | 5 | 123 | R | 連番管理 | 連番（0埋め） |

### 🔷 伝票ヘッダー（1レコード/伝票）

| 項目名 | 型 | 桁数 | オフセット | 寄せ | 内容分類 | セット内容/備考 |
|--------|----|----|----------|------|----------|----------------|
| データ種別 | 文字列 | 2 | 0 | L | 固定値 | `"NN"` |
| レコード種別 | 文字列 | 2 | 2 | L | 固定値 | `"N1"` |
| 小売店コード | 文字列 | 7 | 4 | L | データ参照 | company_code + store_code（0埋め） |
| 企業名 | 文字列 | 20 | 11 | L | データ参照 | vendor_name（カナ変換） |
| 店舗名 | 文字列 | 20 | 31 | L | データ参照 | store_name（カナ変換） |
| 分類コード | 文字列 | 4 | 51 | L | データ参照 | department_code（0埋め） |
| 伝票番号 | 文字列 | 9 | 55 | L | データ参照 | slip_number（0埋め） |
| 仕入先コード | 文字列 | 7 | 64 | L | データ参照 | vendor_code（0埋め） |
| 社店コード | 文字列 | 12 | 71 | L | データ参照 | company_code + store_code（0埋め） |
| 伝票区分 | 文字列 | 2 | 83 | L | 固定値 | `"01"` |
| 取引先コード | 文字列 | 8 | 85 | L | データ参照 | vendor_code（0埋め） |
| 便区分 | 文字列 | 3 | 93 | L | 条件分岐 | 便NO判定 + 補正処理 |
| FILLER | 文字列 | 13 | 96 | L | 固定値 | 空白埋め（13桁） |
| 出力日付区分 | 文字列 | 1 | 109 | L | 固定値 | `" "` |
| 発注日 | 日付 | 6 | 110 | L | データ参照 | slip_date（YYMMDD） |
| 納品予定日 | 日付 | 6 | 116 | L | 計算値 | slip_date + 2日（YYMMDD） |
| 発注区分 | 文字列 | 1 | 122 | L | 条件分岐 | EOS区分により判定 |
| データシリアルNo | 整数 | 5 | 123 | R | 連番管理 | 連番（0埋め） |

### 🔷 明細（Nレコード/伝票）

| 項目名 | 型 | 桁数 | オフセット | 寄せ | 内容分類 | セット内容/備考 |
|--------|----|----|----------|------|----------|----------------|
| データ種別 | 文字列 | 2 | 0 | L | 固定値 | `"NN"` |
| レコード種別 | 文字列 | 2 | 2 | L | 固定値 | `"N4"` |
| FILLER | 文字列 | 5 | 4 | L | 固定値 | 空白埋め（5桁） |
| 商品コード | 文字列 | 14 | 9 | L | データ参照 | product_code |
| 商品名 | 文字列 | 30 | 23 | L | データ参照 | product_name |
| 数量 | 整数 | 4 | 53 | R | データ参照 | quantity（0埋め） |
| 単位 | 文字列 | 2 | 57 | L | データ参照 | unit |
| 単価（仕入） | 整数 | 8 | 59 | R | データ参照 | unit_cost（0埋め） |
| 単価（売価） | 整数 | 8 | 67 | R | データ参照 | unit_price（0埋め） |
| 金額（仕入） | 整数 | 8 | 75 | R | データ参照 | cost_amount（0埋め） |
| 金額（売価） | 整数 | 8 | 83 | R | データ参照 | selling_amount（0埋め） |
| 税区分 | 文字列 | 2 | 91 | L | データ参照 | tax_division_code |
| 税率 | 整数 | 4 | 93 | R | データ参照 | tax_rate * 10（0埋め） |
| FILLER2 | 文字列 | 4 | 97 | L | 固定値 | 空白埋め（4桁） |
| 部門コード | 文字列 | 4 | 101 | L | データ参照 | department_code（0埋め） |
| FILLER3 | 文字列 | 17 | 105 | L | 固定値 | 空白埋め（17桁） |
| データシリアルNo | 整数 | 5 | 123 | R | 連番管理 | 連番（0埋め） |

### 🔷 トレーラー（1レコード/ファイル）

| 項目名 | 型 | 桁数 | オフセット | 寄せ | 内容分類 | セット内容/備考 |
|--------|----|----|----------|------|----------|----------------|
| データ種別 | 文字列 | 2 | 0 | L | 固定値 | `"NN"` |
| レコード種別 | 文字列 | 2 | 2 | L | 固定値 | `"N9"` |
| FILLER1 | 文字列 | 20 | 4 | L | 固定値 | `"999999999999999999999"` |
| 配信レコード件数 | 整数 | 6 | 24 | R | カウント | 総レコード数（0埋め） |
| 配信アイテム件数 | 整数 | 6 | 30 | R | カウント | 明細レコード数（0埋め） |
| FILLER2 | 文字列 | 87 | 36 | L | 固定値 | 空白埋め（87桁） |
| データシリアルNo | 整数 | 5 | 123 | R | 連番管理 | 連番（0埋め） |

---

## 🏗️ システム設計

### 1. ディレクトリ構成

```
shipment_batch/
├── config/
│   ├── config.ini          # 設定ファイル
│   └── logging.conf         # ログ設定
├── src/
│   ├── main.py             # メインプログラム
│   ├── database.py         # DB接続・操作
│   ├── file_generator.py   # ファイル生成
│   ├── models.py           # データモデル
│   └── utils.py            # ユーティリティ
├── output/                 # 出力ディレクトリ
├── logs/                   # ログディレクトリ
├── docs/                   # ドキュメント
├── tests/                  # テストファイル
├── requirements.txt        # 依存関係
└── README.md              # 実行手順書
```

### 2. 設定ファイル（config.ini）

```ini
[DATABASE]
server = localhost
database = OCRInvoice
username = your_username
password = your_password
driver = ODBC Driver 17 for SQL Server
connection_timeout = 30
command_timeout = 300

[OUTPUT]
output_path = C:\BatchOutput\Shipment
backup_path = C:\BatchOutput\Backup
temp_path = C:\BatchOutput\Temp

[LOGGING]
log_level = INFO
log_file = logs/shipment_batch.log
max_file_size = 10MB
backup_count = 5

[SYSTEM]
encoding = shift_jis
record_length = 128
max_records_per_batch = 10000

[EMAIL]
smtp_server = smtp.company.com
smtp_port = 587
from_address = <EMAIL>
to_addresses = <EMAIL>,<EMAIL>
```

### 3. 主要クラス設計

#### DatabaseManager
```python
class DatabaseManager:
    """データベース操作を管理するクラス"""
    
    def get_target_slips(self) -> List[PurchaseSlip]:
        """対象の仕入伝票データを取得"""
        
    def update_send_status(self, slip_ids: List[int]) -> bool:
        """送信フラグを更新"""
        
    def begin_transaction(self) -> None:
        """トランザクション開始"""
        
    def commit_transaction(self) -> None:
        """トランザクションコミット"""
        
    def rollback_transaction(self) -> None:
        """トランザクションロールバック"""
```

#### FileGenerator
```python
class FileGenerator:
    """出力ファイル生成を管理するクラス"""
    
    def generate_shipment_file(self, slips: List[PurchaseSlip]) -> str:
        """出荷データファイルを生成"""
        
    def create_file_header(self, vendor_code: str) -> str:
        """ファイルヘッダーを作成"""
        
    def create_slip_header(self, slip: PurchaseSlip) -> str:
        """伝票ヘッダーを作成"""
        
    def create_detail_record(self, detail: PurchaseSlipDetail) -> str:
        """明細レコードを作成"""
        
    def create_trailer(self, record_count: int, item_count: int) -> str:
        """トレーラーを作成"""
```

#### RecordFormatter
```python
class RecordFormatter:
    """レコードフォーマット処理を管理するクラス"""
    
    def format_string(self, value: str, length: int, align: str = 'L') -> str:
        """文字列フォーマット"""
        
    def format_number(self, value: int, length: int, zero_fill: bool = True) -> str:
        """数値フォーマット"""
        
    def format_date(self, date_value: datetime, format_type: str) -> str:
        """日付フォーマット"""
        
    def convert_to_kana(self, text: str) -> str:
        """カナ変換"""
```

---

## 🔄 処理フロー

### 1. メイン処理フロー

```mermaid
flowchart TD
    A[バッチ開始] --> B[設定ファイル読み込み]
    B --> C[ログ初期化]
    C --> D[データベース接続]
    D --> E[対象データ抽出]
    E --> F{データ存在確認}
    F -->|データあり| G[ファイル生成]
    F -->|データなし| H[正常終了]
    G --> I[送信フラグ更新]
    I --> J[ファイル出力]
    J --> K[バックアップ作成]
    K --> L[処理完了ログ出力]
    L --> H
    
    D --> M{接続エラー}
    M -->|エラー| N[エラーログ出力]
    N --> O[異常終了]
    
    G --> P{ファイル生成エラー}
    P -->|エラー| Q[ロールバック]
    Q --> N
    
    I --> R{更新エラー}
    R -->|エラー| Q
```

### 2. エラーハンドリング

#### データベース関連エラー
- 接続タイムアウト
- SQL実行エラー
- トランザクションエラー

#### ファイル関連エラー
- ディスク容量不足
- ファイル書き込み権限エラー
- 文字コード変換エラー

#### データ関連エラー
- 必須項目の欠損
- データ型不整合
- 桁数オーバー

### 3. ログ出力仕様

#### ログレベル
- **INFO**: 正常処理の進行状況
- **WARNING**: 警告事項（処理は継続）
- **ERROR**: エラー発生（処理中断）
- **DEBUG**: デバッグ情報

#### ログ出力例
```
2024-11-04 20:14:24,123 [INFO] バッチ処理開始
2024-11-04 20:14:24,456 [INFO] 設定ファイル読み込み完了: config/config.ini
2024-11-04 20:14:24,789 [INFO] データベース接続完了
2024-11-04 20:14:25,012 [INFO] 対象伝票数: 15件
2024-11-04 20:14:25,345 [INFO] ファイル生成開始: Shipment_20241104201424.DAT
2024-11-04 20:14:25,678 [INFO] ファイル生成完了: 総レコード数=47件
2024-11-04 20:14:25,901 [INFO] 送信フラグ更新完了: 15件
2024-11-04 20:14:26,234 [INFO] バッチ処理正常終了
```

---

## 📝 実装計画

### Phase 1: 基盤構築（1-2日）
- [ ] プロジェクト構成作成
- [ ] 設定ファイル・ログ機能実装
- [ ] データベース接続機能実装
- [ ] 基本的なエラーハンドリング実装

### Phase 2: データ処理（2-3日）
- [ ] データ抽出機能実装
- [ ] データモデル定義
- [ ] 送信フラグ更新機能実装
- [ ] トランザクション制御実装

### Phase 3: ファイル生成（3-4日）
- [ ] 固定長レコード生成機能
- [ ] ファイルヘッダー・トレーラー生成
- [ ] 文字コード変換（Shift-JIS）
- [ ] レコードフォーマット機能

### Phase 4: テスト・調整（1-2日）
- [ ] 単体テスト実装・実行
- [ ] 結合テスト実行
- [ ] サンプルデータでの動作確認
- [ ] 性能テスト

### Phase 5: ドキュメント・運用準備（1日）
- [ ] 運用手順書作成
- [ ] エラー対応手順書作成
- [ ] 監視設定

---

## 🧪 テスト計画

### 1. 単体テスト

#### DatabaseManager
- [ ] データ抽出機能テスト
- [ ] 送信フラグ更新テスト
- [ ] トランザクション制御テスト
- [ ] 接続エラー処理テスト

#### FileGenerator
- [ ] ファイルヘッダー生成テスト
- [ ] 伝票ヘッダー生成テスト
- [ ] 明細レコード生成テスト
- [ ] トレーラー生成テスト

#### RecordFormatter
- [ ] 文字列フォーマットテスト
- [ ] 数値フォーマットテスト
- [ ] 日付フォーマットテスト
- [ ] 文字コード変換テスト

### 2. 結合テスト

#### 正常系テスト
- [ ] エンドツーエンド処理テスト
- [ ] 複数伝票処理テスト
- [ ] 大量データ処理テスト

#### 異常系テスト
- [ ] データベース接続エラー
- [ ] ファイル出力エラー
- [ ] データ不整合エラー
- [ ] ディスク容量不足

### 3. 性能テスト
- [ ] 1000件データ処理時間測定
- [ ] 10000件データ処理時間測定
- [ ] メモリ使用量測定
- [ ] CPU使用率測定

---

## 🚀 運用要件

### 1. 実行スケジュール
- **実行頻度**: 日次実行
- **実行時刻**: 毎日 AM 6:00
- **実行方法**: Windows タスクスケジューラー
- **実行ユーザー**: バッチ専用サービスアカウント

### 2. 監視項目

#### 処理監視
- [ ] バッチ実行結果（成功/失敗）
- [ ] 処理時間監視（閾値: 30分）
- [ ] 出力ファイルサイズ監視
- [ ] エラーログ監視

#### システム監視
- [ ] ディスク使用量監視
- [ ] メモリ使用量監視
- [ ] データベース接続状況

### 3. アラート設定
- **エラー発生時**: 即座にメール通知
- **処理時間超過**: 30分経過でアラート
- **ディスク容量**: 使用率90%でアラート

### 4. 保守運用

#### 日次作業
- [ ] バッチ実行結果確認
- [ ] エラーログ確認
- [ ] 出力ファイル確認

#### 週次作業
- [ ] ログファイルローテーション確認
- [ ] バックアップファイル整理
- [ ] 性能データ確認

#### 月次作業
- [ ] 処理統計レポート作成
- [ ] システムリソース使用状況確認
- [ ] 設定ファイル見直し

---

## 📚 関連ドキュメント

- [ファイルレイアウト定義書](./ファイルレイアウト定義書.md)
- [データベース設計書](../app/models/purchase_slip.py)
- [運用手順書](./operation_manual.md)
- [エラー対応手順書](./error_handling_manual.md)
- [テスト仕様書](./test_specification.md)

---

## 📞 連絡先

### 開発チーム
- **担当者**: AI開発チーム
- **メール**: <EMAIL>

### 運用チーム
- **担当者**: システム運用チーム
- **メール**: <EMAIL>

---

*最終更新日: 2024-11-04*
*バージョン: 1.0* 