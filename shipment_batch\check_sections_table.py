#!/usr/bin/env python3
"""
Sectionsテーブルの存在確認スクリプト
"""
import sys
import os

# プロジェクトルートをパスに追加
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils import ConfigManager
from database import DatabaseManager

def check_sections_table():
    """Sectionsテーブルの存在を確認する"""
    print("=== Sectionsテーブル存在確認開始 ===")
    
    try:
        # 設定管理初期化
        config = ConfigManager()
        
        # データベース管理初期化
        db_manager = DatabaseManager(config)
        db_manager.connect()
        
        # メインデータベース（YszOCRInvoice）の確認
        print("=== メインデータベース（YszOCRInvoice）の確認 ===")
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # テーブル一覧を取得
            print("データベース内のテーブル一覧:")
            cursor.execute("""
                SELECT TABLE_SCHEMA, TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_TYPE = 'BASE TABLE'
                ORDER BY TABLE_SCHEMA, TABLE_NAME
            """)
            
            tables = cursor.fetchall()
            sections_found = False
            
            for table in tables:
                schema = table.TABLE_SCHEMA
                name = table.TABLE_NAME
                print(f"  {schema}.{name}")
                
                if name.lower() == 'sections':
                    sections_found = True
                    print(f"    ★ Sectionsテーブル発見: {schema}.{name}")
            
            if not sections_found:
                print("❌ メインデータベースにSectionsテーブルが見つかりません")
        
        # 在庫データベース（YszInventory）の確認
        print("\n=== 在庫データベース（YszInventory）の確認 ===")
        if db_manager.inventory_connection:
            cursor = db_manager.inventory_connection.cursor()
            
            # テーブル一覧を取得
            print("在庫データベース内のテーブル一覧:")
            cursor.execute("""
                SELECT TABLE_SCHEMA, TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_TYPE = 'BASE TABLE'
                ORDER BY TABLE_SCHEMA, TABLE_NAME
            """)
            
            tables = cursor.fetchall()
            sections_found = False
            
            for table in tables:
                schema = table.TABLE_SCHEMA
                name = table.TABLE_NAME
                print(f"  {schema}.{name}")
                
                if name.lower() == 'sections':
                    sections_found = True
                    print(f"    ★ Sectionsテーブル発見: {schema}.{name}")
            
            if sections_found:
                print("✅ 在庫データベースにSectionsテーブルが見つかりました")
                
                # Sectionsテーブルの構造を確認
                print("\nSectionsテーブルの構造:")
                cursor.execute("""
                    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = 'Sections'
                    ORDER BY ORDINAL_POSITION
                """)
                
                columns = cursor.fetchall()
                for col in columns:
                    print(f"  {col.COLUMN_NAME}: {col.DATA_TYPE} (NULL: {col.IS_NULLABLE})")
                
                # サンプルデータを確認
                print("\nSectionsテーブルのサンプルデータ:")
                cursor.execute("""
                    SELECT TOP 5 Id, SectionCode, SectionName, DelFlg
                    FROM dbo.Sections
                    WHERE DelFlg = 0
                    ORDER BY Id
                """)
                
                sample_rows = cursor.fetchall()
                for row in sample_rows:
                    print(f"  ID: {row.Id}, Code: {row.SectionCode}, Name: {row.SectionName}, DelFlg: {row.DelFlg}")
            else:
                print("❌ 在庫データベースにもSectionsテーブルが見つかりません")
        else:
            print("❌ 在庫データベースに接続できませんでした")
        
        print("=== Sectionsテーブル存在確認完了 ===")
        
    except Exception as e:
        print(f"確認中にエラーが発生: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if 'db_manager' in locals():
            db_manager.disconnect()

if __name__ == "__main__":
    check_sections_table() 