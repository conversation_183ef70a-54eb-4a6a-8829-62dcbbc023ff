#!/usr/bin/env python3
"""
section_code出力テスト用スクリプト
"""
import sys
import os

# プロジェクトルートをパスに追加
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from models import PurchaseSlip, PurchaseSlipDetail
from utils import ConfigManager
from database import DatabaseManager
from file_generator import FileGenerator
from datetime import datetime
from decimal import Decimal

def test_section_code_output():
    """section_codeの出力をテストする"""
    print("=== section_code出力テスト開始 ===")
    
    try:
        # 設定管理初期化
        config = ConfigManager()
        
        # データベース管理初期化
        db_manager = DatabaseManager(config)
        db_manager.connect()
        
        # ファイル生成器初期化
        file_generator = FileGenerator(config, db_manager)
        
        # テスト用の明細データを作成
        test_detail = PurchaseSlipDetail(
            id=1,
            slip_id=1,
            line_number=1,
            product_code="1234567890123",
            product_name="テスト商品",
            unit="個",
            quantity=Decimal('10'),
            unit_cost=Decimal('100.50'),
            unit_price=Decimal('150'),
            amount=Decimal('1005'),
            item_id=1,
            section_id=123,  # テスト用のsection_id
            org_product_code=None,
            org_product_name=None,
            org_unit=None,
            org_quantity=None,
            org_unit_cost=None,
            org_unit_price=None,
            org_amount=None
        )
        
        # テスト用の伝票データを作成
        test_slip = PurchaseSlip(
            id=1,
            slip_number="12345",
            slip_date=datetime.now(),
            company_code="01",
            vendor_code="1234",
            vendor_name="テスト取引先",
            store_code="01",
            store_name="テスト店舗",
            store_name_kana="テストテンポ",
            department_code="123",
            cost_amount=Decimal('1005'),
            selling_amount=Decimal('1500'),
            tax_division_code="01",
            tax_rate=Decimal('10'),
            status="approved",
            created_at=datetime.now(),
            updated_at=datetime.now(),
            send_flg=False,
            approved_at=datetime.now(),
            approved_by=1,
            err_description=None,
            is_deleted=0,
            send_datetime=None,
            details=[test_detail]
        )
        
        # 明細レコードを生成
        detail_record = file_generator.create_detail_record(test_detail, test_slip, 1)
        
        print(f"生成された明細レコード（128桁）:")
        print(f"'{detail_record}'")
        print(f"レコード長: {len(detail_record)}")
        
        # 110桁目から8桁の部分を抽出（0ベースなので109から117まで）
        class_field = detail_record[109:117]
        print(f"クラス部分（110-117桁目）: '{class_field}'")
        
        # section_idからsection_codeを取得してテスト
        if test_detail.section_id:
            section_code = db_manager.get_section_code(test_detail.section_id)
            print(f"section_id {test_detail.section_id} -> section_code: '{section_code}'")
        
        print("=== section_code出力テスト完了 ===")
        
    except Exception as e:
        print(f"テスト中にエラーが発生: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if 'db_manager' in locals():
            db_manager.disconnect()

if __name__ == "__main__":
    test_section_code_output() 