from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginManager
from flask_wtf.csrf import CSRFProtect

db = SQLAlchemy()
login_manager = LoginManager()
csrf = CSRFProtect()

def create_app():
    app = Flask(__name__, 
        template_folder='templates',  # テンプレートディレクトリを指定
        static_folder='static'        # 静的ファイルディレクトリを指定
    )
    
    # 設定の読み込み
    app.config.from_object('config.Config')
    
    # 各種拡張機能の初期化
    db.init_app(app)
    login_manager.init_app(app)
    csrf.init_app(app)
    
    login_manager.login_view = 'auth.login'
    
    # ブループリントの登録
    from app.views import main_bp
    from app.views.auth import auth_bp
    
    app.register_blueprint(main_bp)
    app.register_blueprint(auth_bp)
    
    # ユータベースの作成
    with app.app_context():
        db.create_all()
    
    # ユーザーローダーの設定
    @login_manager.user_loader
    def load_user(user_id):
        from app.models import User
        return User.query.get(int(user_id))
    
    return app 