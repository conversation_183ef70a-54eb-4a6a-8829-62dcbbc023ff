from flask import Blueprint, request, jsonify, current_app
import pandas as pd
from werkzeug.utils import secure_filename
import os
import tempfile
from flask_login import login_required
from flask_wtf.csrf import CSRFProtect
import logging
import uuid
import threading
import time
from functools import wraps
from app import db
from sqlalchemy import text
from sqlalchemy import create_engine
from decimal import Decimal

bp = Blueprint('purchase', __name__)

# インポートタスクの状態を保持する辞書
import_tasks = {}

# 非同期でCSVインポート処理を実行する関数
def process_csv_import(task_id, files):
    try:
        success_count = 0
        error_files = []
        total_files = len(files)
        processed_files = 0
        total_rows = 0
        processed_rows = 0

        # 処理開始時に状態を更新
        import_tasks[task_id] = {
            'status': 'processing',
            'started_at': time.time(),
            'progress': 'ファイル処理の準備中...',
            'processed_files': 0,
            'total_files': total_files
        }

        # 最初にデータベース接続とテーブルアクセスをテスト
        current_app.logger.info('データベース接続テストを開始します')
        import_tasks[task_id]['progress'] = 'データベース接続テスト中...'

        try:
            # SQLAlchemyのエンジンを使用して接続
            engine = db.get_engine(bind='OCRInvoice')
            conn = engine.raw_connection()
            cursor = conn.cursor()
            current_app.logger.info('データベース接続が成功しました')

            # テーブルアクセステスト - work_purchase_headerとwork_purchase_detailテーブルを確認
            cursor.execute("SELECT TABLE_SCHEMA, TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME IN ('work_purchase_header', 'work_purchase_detail')")
            table_info_list = cursor.fetchall()

            if len(table_info_list) == 2:
                schema_name = table_info_list[0][0]  # 両テーブルとも同じスキーマにあると仮定
                current_app.logger.info(f'work_purchase_headerとwork_purchase_detailテーブルが存在することを確認しました (スキーマ: {schema_name})')
                import_tasks[task_id]['progress'] = f'データベース接続テスト成功: {schema_name}.work_purchase_headerと{schema_name}.work_purchase_detailテーブルにアクセス可能です'
            else:
                error_message = 'work_purchase_headerまたはwork_purchase_detailテーブルが存在しません'
                current_app.logger.error(error_message)
                import_tasks[task_id]['progress'] = f'データベースエラー: {error_message}'
                raise Exception(error_message)

            # テーブルをTRUNCATE（独立したトランザクションとして実行）
            try:
                current_app.logger.info(f'{schema_name}.work_purchase_headerテーブルと{schema_name}.work_purchase_detailテーブルをTRUNCATEします')
                import_tasks[task_id]['progress'] = 'テーブルをクリアしています...'

                # 現在の接続を閉じる
                cursor.close()
                conn.close()

                # 新しい接続を開く（TRUNCATE用）
                truncate_conn = engine.raw_connection()
                truncate_conn.autocommit = False  # 自動コミットを無効化
                truncate_cursor = truncate_conn.cursor()

                # 現在のレコード数を確認
                truncate_cursor.execute(f"SELECT COUNT(*) FROM {schema_name}.work_purchase_header")
                before_count_header = truncate_cursor.fetchone()[0]
                truncate_cursor.execute(f"SELECT COUNT(*) FROM {schema_name}.work_purchase_detail")
                before_count_detail = truncate_cursor.fetchone()[0]
                current_app.logger.info(f'TRUNCATE前のレコード数: ヘッダー={before_count_header}, 明細={before_count_detail}')

                # 外部キー制約があるため、明細テーブルを先にTRUNCATE
                truncate_cursor.execute(f"TRUNCATE TABLE {schema_name}.work_purchase_detail")
                truncate_cursor.execute(f"TRUNCATE TABLE {schema_name}.work_purchase_header")

                # 明示的にコミット
                truncate_conn.commit()
                current_app.logger.info(f'{schema_name}.work_purchase_headerテーブルと{schema_name}.work_purchase_detailテーブルのTRUNCATEをコミットしました')

                # トランザクションカウントをリセット
                truncate_cursor.execute("IF @@TRANCOUNT > 0 COMMIT TRAN")
                current_app.logger.info('トランザクションカウントをリセットしました。')

                # TRUNCATEが成功したか確認
                truncate_cursor.execute(f"SELECT COUNT(*) FROM {schema_name}.work_purchase_header")
                after_count_header = truncate_cursor.fetchone()[0]
                truncate_cursor.execute(f"SELECT COUNT(*) FROM {schema_name}.work_purchase_detail")
                after_count_detail = truncate_cursor.fetchone()[0]
                current_app.logger.info(f'TRUNCATE後のレコード数: ヘッダー={after_count_header}, 明細={after_count_detail}')

                if after_count_header == 0 and after_count_detail == 0:
                    current_app.logger.info('テーブルのTRUNCATEが成功しました')
                    import_tasks[task_id]['progress'] = 'テーブルのクリアが完了しました'
                else:
                    error_message = f'TRUNCATEが実行されましたが、テーブルが空になっていません。ヘッダーレコード数: {after_count_header}, 明細レコード数: {after_count_detail}'
                    current_app.logger.error(error_message)
                    import_tasks[task_id]['progress'] = f'警告: {error_message}'

                # TRUNCATE用の接続を閉じる
                truncate_cursor.close()
                truncate_conn.close()

                # 新しい接続を開く（メイン処理用）
                conn = engine.raw_connection()
                cursor = conn.cursor()
            except Exception as e:
                error_message = f'テーブルのTRUNCATE中にエラーが発生しました: {str(e)}'
                current_app.logger.error(error_message)
                import_tasks[task_id]['progress'] = f'エラー: {error_message}'
                import_tasks[task_id]['status'] = 'error'
                return jsonify({'status': 'error', 'message': error_message})

            current_app.logger.info('TRUNCATE処理が完了しました。メイン処理を続行します。')

        except Exception as e:
            error_message = f'データベース接続テストでエラーが発生しました: {str(e)}'
            current_app.logger.error(error_message)
            import_tasks[task_id]['progress'] = f'データベースエラー: {error_message}'
            raise Exception(error_message)

        # まず全ファイルの行数を計算
        for file_data in files:
            if file_data.filename.lower().endswith('.csv'):
                try:
                    # 一時ファイルとして保存
                    temp_dir = tempfile.gettempdir()
                    temp_path = os.path.join(temp_dir, f"temp_{secure_filename(file_data.filename)}")
                    file_data.save(temp_path)

                    # 行数をカウント（Pandasを使用して正確にカウント）
                    try:
                        df_count = pd.read_csv(temp_path, encoding='shift-jis', dtype=str, keep_default_na=False)
                        row_count = len(df_count)
                        total_rows += row_count
                        current_app.logger.info(f'ファイル {file_data.filename} の行数: {row_count}行')
                    except Exception as count_error:
                        # エラーが発生した場合は従来の方法でカウント
                        current_app.logger.warning(f'Pandasでの行数カウントに失敗しました: {str(count_error)}')
                        with open(temp_path, 'r', encoding='shift-jis') as f:
                            row_count = sum(1 for _ in f) - 1  # ヘッダー行を除く
                            total_rows += row_count
                            current_app.logger.info(f'従来の方法でカウント: ファイル {file_data.filename} の行数: {row_count}行')

                    # 一時ファイルを削除
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                except Exception as e:
                    current_app.logger.error(f'ファイル行数計算エラー: {str(e)}')
                    # エラーが発生しても続行

        # 進捗状況を更新
        import_tasks[task_id]['progress'] = f'処理対象: {total_files}ファイル, 合計{total_rows}行'
        import_tasks[task_id]['total_rows'] = total_rows

        for file_data in files:
            current_app.logger.info(f'ファイル処理開始: {file_data.filename}')

            # 進捗状況を更新
            processed_files += 1
            import_tasks[task_id]['progress'] = f'ファイル処理中 ({processed_files}/{total_files}): {file_data.filename}'
            import_tasks[task_id]['processed_files'] = processed_files

            if not file_data.filename.lower().endswith('.csv'):
                error_files.append(f'{file_data.filename}: CSVファイルではありません。')
                continue

            temp_path = None
            conn = None
            cursor = None

            try:
                # 一時ファイルとして保存
                temp_dir = tempfile.gettempdir()
                temp_path = os.path.join(temp_dir, secure_filename(file_data.filename))
                file_data.save(temp_path)
                current_app.logger.info(f'一時ファイル保存完了: {temp_path}')

                # CSVファイルを読み込み（すべての列を文字列として扱う）
                df = pd.read_csv(temp_path, encoding='shift-jis', dtype=str, keep_default_na=False)
                file_rows = len(df)
                current_app.logger.info(f'CSVファイル読み込み完了: 行数={file_rows}')

                # 進捗状況を更新
                import_tasks[task_id]['progress'] = f'ファイル処理中 ({processed_files}/{total_files}): {file_data.filename} - データ検証中'

                # データが空でないか確認
                if df.empty:
                    error_files.append(f'{file_data.filename}: CSVファイルにデータがありません。')
                    continue

                # ヘッダーの検証
                expected_headers = [
                    "社店コード", "ＰＯＳ", "伝票番号", "取引先CD", "日付",
                    "ＪＡＮ1", "数量1", "原単価1", "売単価1",
                    "ＪＡＮ2", "数量2", "原単価2", "売単価2",
                    "ＪＡＮ3", "数量3", "原単価3", "売単価",
                    "ＪＡＮ4", "数量4", "原単価4", "売単価4",
                    "ＪＡＮ5", "数量5", "原単価5", "売単価5",
                    "ＪＡＮ6", "数量6", "原単価6", "売単価6",
                    "原価金額合計"
                ]

                if list(df.columns) != expected_headers:
                    current_app.logger.error('ヘッダーが一致しません')
                    current_app.logger.error(f'期待するヘッダー: {expected_headers}')
                    current_app.logger.error(f'実際のヘッダー: {list(df.columns)}')
                    error_files.append(f'{file_data.filename}: ヘッダーが正しくありません。')
                    continue

                # 進捗状況を更新
                import_tasks[task_id]['progress'] = f'ファイル処理中 ({processed_files}/{total_files}): {file_data.filename} - データベース接続中'

                # データベース接続
                current_app.logger.info('データベース接続を開始します')
                # SQLAlchemyのエンジンを使用して接続
                engine = db.get_engine(bind='OCRInvoice')
                conn = engine.raw_connection()
                cursor = conn.cursor()
                current_app.logger.info('データベース接続が成功しました')

                # 自動コミットモードを無効化（トランザクション制御を明示的に行う）
                conn.autocommit = False

                # 既存のトランザクションをクリーンアップ
                cursor.execute("IF @@TRANCOUNT > 0 COMMIT TRAN")

                # トランザクション開始（明示的に開始）
                cursor.execute("BEGIN TRANSACTION")
                current_app.logger.info('トランザクションを明示的に開始しました')

                rows_inserted = 0
                last_values = None

                try:
                    # データの挿入処理
                    for index, row in df.iterrows():
                        try:
                            # 元の値をそのまま使用（空文字列の場合は空文字列のまま）
                            values = [val.strip() if isinstance(val, str) else val for val in row]
                            current_app.logger.debug(f'挿入する値: {values}')
                            last_values = values  # 最後の値を保存

                            # ヘッダー情報の抽出
                            header_values = [
                                values[0],  # store_code
                                values[1],  # pos_code
                                values[2],  # slip_number
                                values[3],  # supplier_code
                                values[4],  # purchase_date
                                values[29]  # total_cost_amount
                            ]

                            # ヘッダーレコードの挿入（OUTPUT句を使用してIDを直接取得）
                            insert_header_query = f"""
                                INSERT INTO {schema_name}.work_purchase_header (
                                    store_code, pos_code, slip_number, supplier_code,
                                    purchase_date, total_cost_amount
                                )
                                OUTPUT INSERTED.id
                                VALUES (?, ?, ?, ?, ?, ?);
                            """

                            # ヘッダーレコードを挿入し、生成されたIDを直接取得
                            cursor.execute(insert_header_query, header_values)
                            result = cursor.fetchone()

                            # IDが取得できたか確認
                            if not result or result[0] is None:
                                error_message = f'ヘッダーレコードのID取得に失敗しました。slip_number: {values[2]}'
                                current_app.logger.error(error_message)
                                raise Exception(error_message)

                            header_id = result[0]
                            current_app.logger.info(f'ヘッダーID取得成功: {header_id} (slip_number: {values[2]})')

                            # 明細レコードの挿入
                            for line_no in range(1, 7):
                                # 各行の明細データのインデックスを計算
                                jan_idx = 5 + (line_no - 1) * 4
                                qty_idx = 6 + (line_no - 1) * 4
                                cost_idx = 7 + (line_no - 1) * 4
                                sale_idx = 8 + (line_no - 1) * 4

                                # 明細データを抽出
                                jan_code = values[jan_idx] if jan_idx < len(values) else ''
                                quantity = values[qty_idx] if qty_idx < len(values) else ''
                                cost_price = values[cost_idx] if cost_idx < len(values) else ''
                                sale_price = values[sale_idx] if sale_idx < len(values) else ''

                                # ヘッダーIDが有効か再確認
                                if header_id is None or not isinstance(header_id, (int, float)):
                                    error_message = f'明細レコード挿入時にヘッダーIDが無効です: {header_id}, slip_number: {values[2]}'
                                    current_app.logger.error(error_message)
                                    raise Exception(error_message)

                                # 明細レコードの挿入（空のJANコードでも挿入）
                                insert_detail_query = f"""
                                    INSERT INTO {schema_name}.work_purchase_detail (
                                        work_purchase_header_id, line_no, jan_code,
                                        quantity, cost_price, sale_price
                                    ) VALUES (?, ?, ?, ?, ?, ?)
                                """

                                detail_values = [
                                    header_id,
                                    line_no,
                                    jan_code,
                                    quantity,
                                    cost_price,
                                    sale_price
                                ]

                                cursor.execute(insert_detail_query, detail_values)

                                # 明細レコードが6件ごとに、進捗状況をログに記録
                                if line_no == 6:
                                    current_app.logger.debug(f'明細レコード6件の挿入完了: ヘッダーID={header_id}, slip_number={values[2]}')

                            rows_inserted += 1
                            processed_rows += 1

                            if rows_inserted % 100 == 0:
                                current_app.logger.info(f'{rows_inserted}行挿入完了')
                                # 進捗状況を更新（100行ごと）
                                progress_percent = int(processed_rows / total_rows * 100) if total_rows > 0 else 0
                                import_tasks[task_id]['progress'] = f'データ挿入中: {file_data.filename} - {rows_inserted}/{file_rows}行 (全体: {progress_percent}%)'
                                import_tasks[task_id]['processed_rows'] = processed_rows
                                import_tasks[task_id]['total_rows'] = total_rows

                        except Exception as e:
                            error_message = f'データ挿入中にエラーが発生しました (行 {index+1}): {str(e)}'
                            current_app.logger.error(error_message)
                            current_app.logger.error(f'エラーが発生した行のデータ: {row}')
                            import_tasks[task_id]['progress'] = f'エラー: {error_message}'
                            import_tasks[task_id]['status'] = 'error'
                            conn.rollback()
                            return jsonify({'status': 'error', 'message': error_message})

                    # すべての行の挿入が完了したらコミット
                    current_app.logger.info('すべての行の挿入が完了しました。コミットを実行します。')
                    conn.commit()
                    current_app.logger.info(f'トランザクションをコミットしました。{rows_inserted}行挿入完了')

                    # コミット後に明示的にトランザクションカウントをリセット
                    cursor.execute("IF @@TRANCOUNT > 0 COMMIT TRAN")
                    current_app.logger.info('トランザクションカウントをリセットしました。')

                    # コミット後に確認クエリを実行
                    if last_values and len(last_values) > 2:
                        verify_query = f"SELECT COUNT(*) FROM {schema_name}.work_purchase_header WHERE slip_number = ?"
                        cursor.execute(verify_query, [last_values[2]])  # slip_numberは3番目の値
                        header_count = cursor.fetchone()[0]

                        if header_count > 0:
                            # ヘッダーIDを取得
                            cursor.execute(f"SELECT id FROM {schema_name}.work_purchase_header WHERE slip_number = ?", [last_values[2]])
                            header_id = cursor.fetchone()[0]

                            # 関連する明細レコード数を確認
                            cursor.execute(f"SELECT COUNT(*) FROM {schema_name}.work_purchase_detail WHERE work_purchase_header_id = ?", [header_id])
                            detail_count = cursor.fetchone()[0]

                            current_app.logger.info(f'コミット確認: slip_number={last_values[2]}のヘッダーレコードが{header_count}件、関連する明細レコードが{detail_count}件存在します')

                            # 期待される明細レコード数（6件）と一致するか確認
                            if detail_count == 6:
                                current_app.logger.info('期待される明細レコード数（6件）と一致しています')
                            else:
                                current_app.logger.warning(f'期待される明細レコード数（6件）と一致していません。実際の明細レコード数: {detail_count}件')

                    success_count += 1

                except Exception as tx_error:
                    # エラーが発生した場合はロールバック
                    current_app.logger.error(f'トランザクション処理でエラーが発生しました: {str(tx_error)}')
                    try:
                        conn.rollback()
                        current_app.logger.info('トランザクションをロールバックしました')
                        # ロールバック後に明示的にトランザクションカウントをリセット
                        cursor.execute("IF @@TRANCOUNT > 0 COMMIT TRAN")
                        current_app.logger.info('トランザクションカウントをリセットしました。')
                    except Exception as rollback_error:
                        current_app.logger.error(f'ロールバックエラー: {str(rollback_error)}')
                    raise

                # 進捗状況を更新
                progress_percent = int(processed_rows / total_rows * 100) if total_rows > 0 else 0
                import_tasks[task_id]['progress'] = f'ファイル処理完了 ({processed_files}/{total_files}): {file_data.filename} - {rows_inserted}行挿入 (全体: {progress_percent}%)'

            except Exception as e:
                current_app.logger.error(f'ファイル処理エラー: {str(e)}')
                error_files.append(f'{file_data.filename}: {str(e)}')

                # エラー発生時も進捗状況を更新
                import_tasks[task_id]['progress'] = f'エラー発生: {file_data.filename} - {str(e)}'

            finally:
                if cursor:
                    cursor.close()
                if conn:
                    conn.close()
                    current_app.logger.info('データベース接続を閉じました')
                if temp_path and os.path.exists(temp_path):
                    os.remove(temp_path)
                    current_app.logger.info('一時ファイルを削除しました')

        # 処理結果を保存
        if error_files:
            error_message = f'インポートに失敗したファイルがあります。\n{", ".join(error_files)}'
            current_app.logger.error(error_message)
            import_tasks[task_id].update({
                'status': 'failed',
                'error': error_message,
                'completed_at': time.time()
            })
        else:
            success_message = f'全ての{success_count}ファイルのインポートが完了しました。合計{processed_rows}行のデータを処理しました。'
            current_app.logger.info(success_message)
            import_tasks[task_id].update({
                'status': 'completed',
                'message': success_message,
                'completed_at': time.time()
            })

    except Exception as e:
        error_message = f'インポート処理でエラーが発生しました: {str(e)}'
        current_app.logger.error(error_message)
        import_tasks[task_id].update({
            'status': 'failed',
            'error': error_message,
            'completed_at': time.time()
        })

# ファイルデータを保存するためのクラス
class FileData:
    def __init__(self, filename, content):
        self.filename = filename
        self.content = content

    def save(self, path):
        with open(path, 'wb') as f:
            f.write(self.content)

# アプリケーションコンテキスト内でCSVインポート処理を実行するためのラッパー関数
def process_csv_import_with_app_context(app, task_id, file_data_list):
    with app.app_context():
        try:
            process_csv_import(task_id, file_data_list)
        except Exception as e:
            # アプリケーションコンテキスト内でエラーをログに記録
            current_app.logger.error(f'CSVインポート処理でエラーが発生しました: {str(e)}')
            import_tasks[task_id] = {
                'status': 'failed',
                'error': f'インポート処理でエラーが発生しました: {str(e)}',
                'completed_at': time.time()
            }

@bp.route('/slips/import-csv', methods=['POST'])
@login_required
def import_purchase():
    current_app.logger.info('CSVインポート処理を開始します')

    if 'files[]' not in request.files:
        current_app.logger.warning('ファイルが選択されていません')
        return jsonify({'error': 'ファイルが選択されていません。'})

    files = request.files.getlist('files[]')
    if not files:
        current_app.logger.warning('ファイルが選択されていません')
        return jsonify({'error': 'ファイルが選択されていません。'})

    # ファイル形式チェックとファイルデータのコピー
    file_data_list = []
    for file in files:
        if not file.filename.lower().endswith('.csv'):
            return jsonify({'error': f'{file.filename}: CSVファイルではありません。CSVファイルのみアップロード可能です。'})

        # ファイルの内容をメモリにコピー
        file_content = file.read()
        file.seek(0)  # ファイルポインタをリセット

        # FileDataオブジェクトを作成
        file_data = FileData(file.filename, file_content)
        file_data_list.append(file_data)

    # タスクIDを生成
    task_id = str(uuid.uuid4())

    # タスクの初期状態を設定
    import_tasks[task_id] = {
        'status': 'initializing',
        'started_at': time.time(),
        'progress': 'インポート処理を初期化中...'
    }

    try:
        # 現在のアプリケーションオブジェクトを取得
        app = current_app._get_current_object()

        # 非同期でインポート処理を実行（アプリケーションコンテキスト付き）
        thread = threading.Thread(
            target=process_csv_import_with_app_context,
            args=(app, task_id, file_data_list)
        )
        thread.daemon = True
        thread.start()

        current_app.logger.info(f'インポート処理スレッドを開始しました。タスクID: {task_id}')

        # タスクIDを返却
        return jsonify({
            'message': 'CSVインポート処理を開始しました。処理が完了するまでお待ちください。',
            'task_id': task_id
        })
    except Exception as e:
        current_app.logger.error(f'インポート処理スレッド起動エラー: {str(e)}')
        import_tasks[task_id] = {
            'status': 'failed',
            'error': f'インポート処理の開始に失敗しました: {str(e)}',
            'completed_at': time.time()
        }
        return jsonify({
            'error': 'インポート処理の開始に失敗しました。',
            'task_id': task_id
        }), 500

@bp.route('/slips/import-status/<task_id>', methods=['GET'])
@login_required
def check_import_status(task_id):
    if task_id not in import_tasks:
        return jsonify({
            'error': '指定されたタスクIDが見つかりません。'
        }), 404

    task_info = import_tasks[task_id]

    # 処理完了から30分経過したタスク情報はクリーンアップ
    if task_info.get('status') in ['completed', 'failed']:
        if 'started_at' in task_info and time.time() - task_info['started_at'] > 1800:
            del import_tasks[task_id]
            return jsonify({
                'error': 'タスク情報の有効期限が切れました。'
            }), 404

    return jsonify(task_info)

@bp.route('/convert_work_to_slips', methods=['POST'])
@login_required
def convert_work_to_slips():
    """ワークテーブルから実際の伝票データに変換する処理"""
    current_app.logger.info("=== ワークテーブル変換処理開始 ===")
    try:
        # 変数を事前に初期化
        converted_count = 0
        corrected_count = 0
        error_count = 0
        slip_error_count = 0

        # 他の箇所と同じ方法でデータベース接続を取得
        current_app.logger.info("データベース接続を取得中...")
        engine = db.get_engine(bind='OCRInvoice')
        conn = engine.raw_connection()
        cursor = conn.cursor()
        current_app.logger.info("データベース接続成功")

        try:
            # 自動コミットを無効化
            conn.autocommit = False

            # 既存のトランザクションをクリーンアップ
            cursor.execute("IF @@TRANCOUNT > 0 COMMIT TRAN")

            # トランザクション開始
            cursor.execute("BEGIN TRANSACTION")
            # 問題のある伝票を特定するクエリ（raw connectionを使用）
            mismatch_query = """
            WITH MismatchHeaders AS (
                SELECT
                    d.work_purchase_header_id
                FROM
                    work_purchase_detail d
                INNER JOIN
                    work_purchase_header h ON d.work_purchase_header_id = h.id
                GROUP BY
                    d.work_purchase_header_id,
                    h.total_cost_amount,
                    h.slip_number
                HAVING
                    SUM(CAST(d.quantity AS FLOAT) * CAST(d.cost_price AS FLOAT)) > h.total_cost_amount
            )
            SELECT
                d.work_purchase_header_id,
                h.slip_number,
                h.total_cost_amount,
                d.line_no,
                d.jan_code,
                d.quantity,
                d.cost_price,
                d.sale_price
            FROM
                work_purchase_detail d
            INNER JOIN
                work_purchase_header h ON d.work_purchase_header_id = h.id
            INNER JOIN
                MismatchHeaders mh ON d.work_purchase_header_id = mh.work_purchase_header_id
            ORDER BY
                d.work_purchase_header_id, d.line_no
            """

            cursor.execute(mismatch_query)
            mismatch_data = cursor.fetchall()

            # 問題のある伝票IDを収集
            problematic_header_ids = set()
            for row in mismatch_data:
                problematic_header_ids.add(row[0])  # work_purchase_header_id

            current_app.logger.info(f"桁数問題のある伝票数: {len(problematic_header_ids)}")

            # 全てのワークヘッダーを取得
            headers_query = """
            SELECT id, store_code, pos_code, slip_number, supplier_code, purchase_date, total_cost_amount
            FROM work_purchase_header
            ORDER BY id
            """

            cursor.execute(headers_query)
            headers = cursor.fetchall()

            for header in headers:
                try:
                    current_app.logger.info(f"伝票 {header[3]} の処理を開始します")  # slip_number

                    # 明細データを取得
                    details_query = """
                    SELECT line_no, jan_code, quantity, cost_price, sale_price
                    FROM work_purchase_detail
                    WHERE work_purchase_header_id = ?
                    ORDER BY line_no
                    """

                    cursor.execute(details_query, [header[0]])  # header id
                    details = cursor.fetchall()

                    current_app.logger.info(f"伝票 {header[3]}: {len(details)}件の明細を取得しました")

                    # 原単価と数量の補正処理
                    corrected_details = []
                    is_corrected = False

                    if header[0] in problematic_header_ids:  # header id
                        # 桁数問題がある伝票の場合、補正を試行
                        current_app.logger.info(f"=== 伝票 {header[3]} の補正処理開始 ===")
                        # detailsをオブジェクト風に変換
                        detail_objects = []
                        for d in details:
                            class DetailObj:
                                def __init__(self, line_no, jan_code, quantity, cost_price, sale_price):
                                    self.line_no = line_no
                                    self.jan_code = jan_code
                                    self.quantity = quantity
                                    self.cost_price = cost_price
                                    self.sale_price = sale_price
                            detail_objects.append(DetailObj(d[0], d[1], d[2], d[3], d[4]))

                        corrected_details, is_corrected = correct_unit_cost_and_quantity(
                            detail_objects, header[6]  # total_cost_amount
                        )
                        if is_corrected:
                            corrected_count += 1
                            current_app.logger.info(f"✓ 伝票 {header[3]} の原単価・数量を補正しました")
                            current_app.logger.info(f"補正後明細数: {len(corrected_details)}")
                        else:
                            current_app.logger.warning(f"× 伝票 {header[3]} の補正に失敗しました。元データを使用します")
                            # 補正に失敗した場合は元データを使用（空文字列を0に変換）
                            corrected_details = []
                            for d in details:
                                try:
                                    line_no = d[0]
                                    jan_code = d[1]
                                    quantity = float(d[2]) if d[2] and str(d[2]).strip() else 0.0
                                    cost_price = float(d[3]) if d[3] and str(d[3]).strip() else 0.0
                                    sale_price = float(d[4]) if d[4] and str(d[4]).strip() else 0.0
                                    corrected_details.append((line_no, jan_code, quantity, cost_price, sale_price))
                                except (ValueError, TypeError) as e:
                                    current_app.logger.warning(f"伝票 {header[3]} 元データ変換エラー: {d}, エラー: {str(e)}")
                                    continue
                    else:
                        # 問題のない伝票はそのまま使用（空文字列を0に変換）
                        corrected_details = []
                        for d in details:
                            try:
                                line_no = d[0]
                                jan_code = d[1]
                                quantity = float(d[2]) if d[2] and str(d[2]).strip() else 0.0
                                cost_price = float(d[3]) if d[3] and str(d[3]).strip() else 0.0
                                sale_price = float(d[4]) if d[4] and str(d[4]).strip() else 0.0
                                corrected_details.append((line_no, jan_code, quantity, cost_price, sale_price))
                            except (ValueError, TypeError) as e:
                                current_app.logger.warning(f"伝票 {header[3]} 明細変換エラー: {d}, エラー: {str(e)}")
                                continue
                        current_app.logger.info(f"伝票 {header[3]}: 補正不要、元データを使用")

                    # 明細データの妥当性チェック
                    if not corrected_details:
                        current_app.logger.error(f"伝票 {header[3]}: 処理可能な明細データがありません。スキップします")
                        continue

                    # 有効な明細数をログ出力
                    valid_details_count = len([d for d in corrected_details if d[1] and str(d[1]).strip()])  # JANコードがある明細
                    current_app.logger.info(f"伝票 {header[3]}: 全明細数={len(corrected_details)}, 有効明細数={valid_details_count}")

                    # 実際の伝票データを作成
                    current_app.logger.info(f"伝票 {header[3]}: 本番テーブルへの変換を開始")
                    # headerをオブジェクト風に変換
                    class HeaderObj:
                        def __init__(self, id, store_code, pos_code, slip_number, supplier_code, purchase_date, total_cost_amount):
                            self.id = id
                            self.store_code = store_code
                            self.pos_code = pos_code
                            self.slip_number = slip_number
                            self.supplier_code = supplier_code
                            self.purchase_date = purchase_date
                            self.total_cost_amount = total_cost_amount

                    header_obj = HeaderObj(header[0], header[1], header[2], header[3], header[4], header[5], header[6])

                    # 本番テーブルへの投入を実行
                    try:
                        create_purchase_slip_from_work(cursor, header_obj, corrected_details)
                        converted_count += 1
                        current_app.logger.info(f"✓ 伝票 {header[3]}: 変換完了 ({converted_count}件目)")
                    except Exception as slip_error:
                        slip_error_count += 1
                        current_app.logger.error(f"✗ 伝票 {header[3]} の本番テーブル投入に失敗: {str(slip_error)}")
                        import traceback
                        current_app.logger.error(f"詳細なエラー情報: {traceback.format_exc()}")
                        # 個別の伝票エラーは処理を継続（全体のトランザクションは維持）
                        continue

                except Exception as e:
                    error_count += 1
                    current_app.logger.error(f"伝票 {header[3]} の処理中に予期しないエラー: {str(e)}")
                    import traceback
                    current_app.logger.error(f"詳細なエラー情報: {traceback.format_exc()}")
                    continue

            # トランザクションをコミット
            conn.commit()
            current_app.logger.info(f"=== 変換処理完了サマリー ===")
            current_app.logger.info(f"処理対象伝票数: {len(headers)}")
            current_app.logger.info(f"補正対象伝票数: {len(problematic_header_ids)}")
            current_app.logger.info(f"補正成功件数: {corrected_count}")
            current_app.logger.info(f"変換成功件数: {converted_count}")
            current_app.logger.info(f"本番テーブル投入失敗件数: {slip_error_count}")
            current_app.logger.info(f"その他エラー件数: {error_count}")
            current_app.logger.info(f"総失敗件数: {len(headers) - converted_count}")

        except Exception as inner_e:
            # エラーが発生した場合はロールバック
            conn.rollback()
            current_app.logger.error(f"変換処理中にエラー: {str(inner_e)}")
            import traceback
            current_app.logger.error(f"詳細なエラー情報: {traceback.format_exc()}")
            raise
        finally:
            # 接続を閉じる
            if cursor:
                cursor.close()
            if conn:
                conn.close()

        return jsonify({
            'status': 'success',
            'message': f'変換完了: {converted_count}件の伝票を変換しました（うち{corrected_count}件を補正）',
            'converted_count': converted_count,
            'corrected_count': corrected_count
        })

    except Exception as e:
        current_app.logger.error(f"ワークテーブル変換中にエラー: {str(e)}")
        return jsonify({'error': 'ワークテーブルの変換中にエラーが発生しました'}), 500

def correct_unit_cost_and_quantity(details, total_cost_amount):
    """原単価と数量の桁数問題を補正する関数"""
    current_app.logger.info(f"補正処理開始: 明細数={len(details)}, 合計金額={total_cost_amount}")

    # 有効な明細のみを抽出（JANコードが空でも数量と原単価があれば有効とする）
    valid_details = []
    for detail in details:
        try:
            if detail.quantity and detail.cost_price and float(detail.quantity) > 0 and float(detail.cost_price) > 0:
                valid_details.append(detail)
                current_app.logger.debug(f"有効明細: 行{detail.line_no}, 数量={detail.quantity}, 原単価={detail.cost_price}")
            else:
                current_app.logger.debug(f"無効明細: 行{detail.line_no}, 数量={detail.quantity}, 原単価={detail.cost_price}")
        except (ValueError, TypeError) as e:
            current_app.logger.warning(f"明細データ変換エラー: 行{detail.line_no}, エラー={str(e)}")

    if not valid_details:
        current_app.logger.warning("有効な明細が見つかりませんでした")
        return [], False

    current_app.logger.info(f"有効明細数: {len(valid_details)}")

    current_app.logger.info(f"有効な明細数: {len(valid_details)}, 目標金額: {total_cost_amount}")

    # 各パターンで全明細の合計金額を計算
    patterns = []

    # パターン1: 数量を10で割り、原単価を100で割る
    pattern1_total = 0
    pattern1_details = []
    for detail in valid_details:
        quantity = float(detail.quantity) / 10
        cost_price = float(detail.cost_price) / 100
        sale_price = float(detail.sale_price) if detail.sale_price else 0

        # 補正後の値が0.1以上であれば採用（より緩い条件）
        if quantity >= 0.1 and cost_price >= 0.1:
            amount = quantity * cost_price
            pattern1_total += amount
            pattern1_details.append((detail.line_no, detail.jan_code, quantity, cost_price, sale_price))
        else:
            # 合理的でない場合は元の値を使用
            quantity = float(detail.quantity)
            cost_price = float(detail.cost_price)
            amount = quantity * cost_price
            pattern1_total += amount
            pattern1_details.append((detail.line_no, detail.jan_code, quantity, cost_price, sale_price))

    patterns.append((pattern1_details, pattern1_total, "パターン1: 数量÷10, 原単価÷100"))

    # パターン2: 数量はそのまま、原単価を100で割る
    pattern2_total = 0
    pattern2_details = []
    for detail in valid_details:
        quantity = float(detail.quantity)
        cost_price = float(detail.cost_price) / 100
        sale_price = float(detail.sale_price) if detail.sale_price else 0

        # 補正後の値が0.1以上であれば採用（より緩い条件）
        if quantity >= 0.1 and cost_price >= 0.1:
            amount = quantity * cost_price
            pattern2_total += amount
            pattern2_details.append((detail.line_no, detail.jan_code, quantity, cost_price, sale_price))
        else:
            # 合理的でない場合は元の値を使用
            quantity = float(detail.quantity)
            cost_price = float(detail.cost_price)
            amount = quantity * cost_price
            pattern2_total += amount
            pattern2_details.append((detail.line_no, detail.jan_code, quantity, cost_price, sale_price))

    patterns.append((pattern2_details, pattern2_total, "パターン2: 原単価÷100"))

    # パターン3: 数量を10で割り、原単価はそのまま
    pattern3_total = 0
    pattern3_details = []
    for detail in valid_details:
        quantity = float(detail.quantity) / 10
        cost_price = float(detail.cost_price)
        sale_price = float(detail.sale_price) if detail.sale_price else 0

        # 補正後の値が0.1以上であれば採用（より緩い条件）
        if quantity >= 0.1 and cost_price >= 0.1:
            amount = quantity * cost_price
            pattern3_total += amount
            pattern3_details.append((detail.line_no, detail.jan_code, quantity, cost_price, sale_price))
        else:
            # 合理的でない場合は元の値を使用
            quantity = float(detail.quantity)
            cost_price = float(detail.cost_price)
            amount = quantity * cost_price
            pattern3_total += amount
            pattern3_details.append((detail.line_no, detail.jan_code, quantity, cost_price, sale_price))

    patterns.append((pattern3_details, pattern3_total, "パターン3: 数量÷10"))

    # パターン4: 原価金額 ÷ 数量 で原単価を計算（より精密な計算）
    pattern4_total = 0
    pattern4_details = []

    # 全明細の数量合計を計算
    total_quantity = sum(float(detail.quantity) for detail in valid_details)

    if total_quantity > 0:
        # 平均原単価を計算
        average_unit_cost = float(total_cost_amount) / total_quantity

        for detail in valid_details:
            quantity = float(detail.quantity)
            sale_price = float(detail.sale_price) if detail.sale_price else 0

            # 平均原単価を使用
            cost_price = average_unit_cost

            # 補正後の値が0.1以上であれば採用（より緩い条件）
            if quantity >= 0.1 and cost_price >= 0.1:
                amount = quantity * cost_price
                pattern4_total += amount
                pattern4_details.append((detail.line_no, detail.jan_code, quantity, cost_price, sale_price))
            else:
                # 合理的でない場合は元の値を使用
                quantity = float(detail.quantity)
                cost_price = float(detail.cost_price)
                amount = quantity * cost_price
                pattern4_total += amount
                pattern4_details.append((detail.line_no, detail.jan_code, quantity, cost_price, sale_price))
    else:
        # 数量合計が0の場合は元の値を使用
        for detail in valid_details:
            quantity = float(detail.quantity)
            cost_price = float(detail.cost_price)
            sale_price = float(detail.sale_price) if detail.sale_price else 0
            amount = quantity * cost_price
            pattern4_total += amount
            pattern4_details.append((detail.line_no, detail.jan_code, quantity, cost_price, sale_price))

    patterns.append((pattern4_details, pattern4_total, "パターン4: 原価金額÷数量（平均原単価）"))

    # 元のパターン（補正なし）
    original_total = 0
    original_details = []
    for detail in valid_details:
        quantity = float(detail.quantity)
        cost_price = float(detail.cost_price)
        sale_price = float(detail.sale_price) if detail.sale_price else 0
        amount = quantity * cost_price
        original_total += amount
        original_details.append((detail.line_no, detail.jan_code, quantity, cost_price, sale_price))

    patterns.append((original_details, original_total, "元のデータ（補正なし）"))

    # total_cost_amount に最も近いパターンを選択
    target_amount = float(total_cost_amount)
    best_pattern = None
    min_diff = float('inf')

    current_app.logger.info(f"=== 補正パターン比較 (目標金額: {target_amount}) ===")
    for pattern_details, pattern_total, pattern_name in patterns:
        diff = abs(pattern_total - target_amount)
        diff_percent = (diff / target_amount * 100) if target_amount > 0 else 0
        current_app.logger.info(f"{pattern_name}: 合計={pattern_total:.2f}, 差額={diff:.2f} ({diff_percent:.1f}%)")

        if diff < min_diff:
            min_diff = diff
            best_pattern = (pattern_details, pattern_total, pattern_name)

    # 補正の閾値を設定（目標金額の10%以内の差であれば補正を採用）
    improvement_threshold = target_amount * 0.1
    original_pattern = patterns[-1]  # 最後のパターンが元のデータ
    original_diff = abs(original_pattern[1] - target_amount)

    current_app.logger.info(f"=== 補正判定 ===")
    current_app.logger.info(f"最適パターン: {best_pattern[2]}, 差額: {min_diff:.2f}")
    current_app.logger.info(f"元データ差額: {original_diff:.2f}")
    current_app.logger.info(f"改善閾値: {improvement_threshold:.2f}")

    if best_pattern and best_pattern[2] != "元のデータ（補正なし）" and min_diff < original_diff:
        current_app.logger.info(f"✓ 補正採用: {best_pattern[2]}, 目標金額={target_amount}, 計算金額={best_pattern[1]:.2f}, 差額={min_diff:.2f}")
        current_app.logger.info(f"補正結果: {len(best_pattern[0])}件の明細を返却")
        return best_pattern[0], True
    else:
        current_app.logger.warning(f"× 補正不採用: 元のデータを使用 (差額改善なし)")
        current_app.logger.info(f"元データ結果: {len(original_details)}件の明細を返却")
        return original_details, False

def create_purchase_slip_from_work(cursor, header, details):
    """ワークデータから実際の伝票データを作成する関数"""
    from datetime import datetime
    from decimal import Decimal

    # 伝票ヘッダーの作成
    try:
        # 日付の処理を安全に行う
        if header.purchase_date:
            if isinstance(header.purchase_date, str):
                slip_date = datetime.strptime(header.purchase_date, '%Y-%m-%d').date()
            else:
                slip_date = header.purchase_date
        else:
            slip_date = datetime.now().date()
    except Exception as e:
        current_app.logger.warning(f"日付変換エラー: {header.purchase_date}, デフォルト日付を使用します: {str(e)}")
        slip_date = datetime.now().date()

    # 売価金額の計算
    selling_total = sum(Decimal(str(detail[2])) * Decimal(str(detail[4])) for detail in details if detail[4])

    # 伝票ヘッダーの挿入（位置パラメータを使用）
    insert_slip_query = """
    INSERT INTO dbo.purchase_slip (
        slip_number, slip_date, company_code, vendor_code, vendor_name,
        store_code, store_name, department_code, cost_amount, selling_amount,
        tax_division_code, status, is_deleted
    )
    OUTPUT INSERTED.id
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """

    # パラメータを順序通りに配列で渡す（日付を文字列に変換）
    slip_params = [
        str(header.slip_number),
        slip_date.strftime('%Y-%m-%d'),  # 日付を文字列に変換
        '01',  # company_code
        str(header.supplier_code),
        '',  # vendor_name
        str(header.store_code),
        '',  # store_name
        '001',  # department_code
        float(header.total_cost_amount),  # Decimalではなくfloatを使用
        float(selling_total),  # Decimalではなくfloatを使用
        '01',  # tax_division_code
        'pending',  # status
        0  # is_deleted
    ]

    # ヘッダー挿入パラメータのログ出力
    current_app.logger.debug(f"ヘッダー挿入パラメータ: slip_number={header.slip_number}, slip_date={slip_date}, vendor_code={header.supplier_code}, store_code={header.store_code}, cost_amount={header.total_cost_amount}")

    try:
        cursor.execute(insert_slip_query, slip_params)

        # IDを取得
        result = cursor.fetchone()
        if not result:
            raise Exception("ヘッダー挿入後にIDが取得できませんでした")
        slip_id = result[0]
        current_app.logger.debug(f"ヘッダー挿入成功: slip_id={slip_id}")

    except Exception as header_error:
        error_msg = str(header_error)
        current_app.logger.error(f"ヘッダー挿入エラー: 伝票{header.slip_number}, エラー: {error_msg}")

        # 具体的なエラー原因を特定
        if "duplicate" in error_msg.lower() or "unique" in error_msg.lower():
            current_app.logger.error(f"重複エラー: 伝票番号{header.slip_number}は既に存在している可能性があります")
        elif "foreign key" in error_msg.lower():
            current_app.logger.error(f"外部キー制約エラー: 参照先のレコードが存在しません")
        elif "null" in error_msg.lower():
            current_app.logger.error(f"NULL制約エラー: 必須フィールドがNULLです")
        elif "truncated" in error_msg.lower() or "length" in error_msg.lower():
            current_app.logger.error(f"データ長エラー: フィールドの値が長すぎます")

        import traceback
        current_app.logger.error(f"詳細なエラー情報: {traceback.format_exc()}")
        raise

    # 明細データの挿入
    inserted_details_count = 0
    skipped_details_count = 0

    for detail in details:
        try:
            line_no, jan_code, quantity, cost_price, sale_price = detail

            # データ型の安全な変換
            try:
                line_no = int(line_no)
                jan_code = str(jan_code) if jan_code else ''
                quantity = float(quantity)  # Decimalではなくfloatを使用
                cost_price = float(cost_price)  # Decimalではなくfloatを使用
                sale_price = float(sale_price) if sale_price else 0.0  # Decimalではなくfloatを使用
            except (ValueError, TypeError, Exception) as convert_error:
                current_app.logger.error(f"伝票 {header.slip_number} 明細 {detail}: データ型変換エラー: {str(convert_error)}")
                skipped_details_count += 1
                continue

            # 数量と原単価が有効な場合のみ処理（JANコードが空でも処理）
            if quantity <= 0 or cost_price <= 0:
                current_app.logger.warning(f"伝票 {header.slip_number} 明細 {line_no}: 無効な数量({quantity})または原単価({cost_price})のためスキップ")
                skipped_details_count += 1
                continue

            # JANコードが空の場合は警告ログを出力するが処理は継続
            if not jan_code or jan_code.strip() == '':
                current_app.logger.warning(f"伝票 {header.slip_number} 明細 {line_no}: JANコードが空ですが、数量・原単価が有効なため処理を継続")
                jan_code = f"EMPTY{line_no:02d}"  # 空の場合は一意な値を設定（14文字以内）

            # JANコードの長さチェック（14文字制限）
            if len(jan_code) > 14:
                current_app.logger.warning(f"伝票 {header.slip_number} 明細 {line_no}: JANコード長すぎ({len(jan_code)}文字)、切り詰めます")
                jan_code = jan_code[:14]

            amount = quantity * cost_price

            insert_detail_query = """
            INSERT INTO dbo.purchase_slip_detail (
                slip_id, line_number, product_code, product_name, quantity,
                unit_cost, unit_price, amount, org_product_code, org_quantity, org_unit_cost, org_unit_price, org_amount
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            # パラメータを順序通りに配列で渡す
            detail_params = [
                slip_id,
                line_no,
                jan_code,
                '',  # product_name
                quantity,
                cost_price,
                sale_price,
                amount,
                jan_code,  # org_product_code
                quantity,  # org_quantity
                cost_price,  # org_unit_cost
                sale_price,  # org_unit_price
                amount  # org_amount
            ]

            # パラメータの妥当性チェック
            current_app.logger.debug(f"明細挿入パラメータ: slip_id={slip_id}, line_no={line_no}, jan_code='{jan_code}', quantity={quantity}, cost_price={cost_price}, sale_price={sale_price}, amount={amount}")

            cursor.execute(insert_detail_query, detail_params)
            inserted_details_count += 1
            current_app.logger.debug(f"明細挿入成功: 伝票{header.slip_number} 明細{line_no}")

        except Exception as detail_error:
            error_msg = str(detail_error)
            current_app.logger.error(f"明細データ挿入エラー: 伝票{header.slip_number} 明細{detail}, エラー: {error_msg}")

            # 具体的なエラー原因を特定
            if "foreign key" in error_msg.lower():
                current_app.logger.error(f"外部キー制約エラー: slip_id={slip_id}が存在しないか、参照先のレコードが見つかりません")
            elif "null" in error_msg.lower():
                current_app.logger.error(f"NULL制約エラー: 必須フィールド（product_code, quantity, unit_cost, unit_price, amount）のいずれかがNULLです")
            elif "truncated" in error_msg.lower() or "length" in error_msg.lower():
                current_app.logger.error(f"データ長エラー: product_code(14文字制限)などのフィールドが長すぎます")
            elif "numeric" in error_msg.lower() or "decimal" in error_msg.lower():
                current_app.logger.error(f"数値エラー: quantity, unit_cost, unit_price, amountの値が不正です")

            import traceback
            current_app.logger.error(f"詳細なエラー情報: {traceback.format_exc()}")
            skipped_details_count += 1
            continue

    # 明細挿入結果の検証
    if inserted_details_count == 0:
        error_msg = f"伝票 {header.slip_number}: 明細が1件も挿入されませんでした（スキップ: {skipped_details_count}件）"
        current_app.logger.error(error_msg)
        raise Exception(error_msg)

    current_app.logger.info(f"伝票 {header.slip_number}: 明細挿入完了（成功: {inserted_details_count}件, スキップ: {skipped_details_count}件）")

    current_app.logger.info(f"伝票 {header.slip_number} を作成しました (ID: {slip_id})")


@bp.route('/compare_work_and_production', methods=['GET'])
@login_required
def compare_work_and_production():
    """ワークテーブルと本番テーブルのデータを比較して欠落を調査（今回の変換処理に限定）"""
    try:
        # データベース接続
        engine = db.get_engine(bind='OCRInvoice')
        conn = engine.raw_connection()
        cursor = conn.cursor()

        # ワークテーブルの伝票一覧を取得
        work_query = """
        SELECT slip_number, store_code, supplier_code, total_cost_amount
        FROM work_purchase_header
        ORDER BY slip_number
        """
        cursor.execute(work_query)
        work_slips = cursor.fetchall()

        current_app.logger.info(f"ワークテーブル検索結果: {len(work_slips)}件")
        if work_slips:
            current_app.logger.info(f"ワーク伝票例: {work_slips[0][:4]}")  # 最初の4フィールドのみ表示

        # 本番テーブルの伝票一覧を取得（最近作成されたもの）
        production_query = """
        SELECT slip_number, store_code, vendor_code, cost_amount, created_at
        FROM purchase_slip
        WHERE is_deleted = 0
        AND created_at >= DATEADD(hour, -2, GETDATE())
        ORDER BY slip_number
        """
        cursor.execute(production_query)
        production_slips = cursor.fetchall()

        current_app.logger.info(f"本番テーブル検索結果: {len(production_slips)}件")
        if production_slips:
            current_app.logger.info(f"最新の伝票例: {production_slips[0]}")
            current_app.logger.info(f"最古の伝票例: {production_slips[-1]}")

        # さらに、ワークテーブルの伝票番号と一致するもののみに絞り込み
        work_slip_numbers = {slip[0] for slip in work_slips}
        production_slips_filtered = [slip for slip in production_slips if slip[0] in work_slip_numbers]
        production_slip_numbers = {slip[0] for slip in production_slips_filtered}

        current_app.logger.info(f"ワーク伝票番号数: {len(work_slip_numbers)}")
        current_app.logger.info(f"本番テーブル（フィルタ後）: {len(production_slips_filtered)}件")
        current_app.logger.info(f"本番伝票番号数: {len(production_slip_numbers)}")

        # 欠落している伝票を特定
        missing_slips = work_slip_numbers - production_slip_numbers
        extra_slips = production_slip_numbers - work_slip_numbers

        current_app.logger.info(f"欠落伝票数: {len(missing_slips)}")
        if missing_slips:
            current_app.logger.info(f"欠落伝票例: {list(missing_slips)[:5]}")  # 最初の5件

        # 欠落した伝票の詳細情報を取得
        missing_details = []
        if missing_slips:
            for slip_number in missing_slips:
                # ワークテーブルから詳細を取得
                detail_query = """
                SELECT h.slip_number, h.store_code, h.supplier_code, h.total_cost_amount,
                       COUNT(d.id) as detail_count
                FROM work_purchase_header h
                LEFT JOIN work_purchase_detail d ON h.id = d.work_purchase_header_id
                WHERE h.slip_number = ?
                GROUP BY h.slip_number, h.store_code, h.supplier_code, h.total_cost_amount
                """
                cursor.execute(detail_query, [slip_number])
                detail = cursor.fetchone()
                if detail:
                    missing_details.append({
                        'slip_number': detail[0],
                        'store_code': detail[1],
                        'supplier_code': detail[2],
                        'total_cost_amount': detail[3],
                        'detail_count': detail[4]
                    })

        # 統計情報
        stats = {
            'work_table_count': len(work_slips),
            'production_table_count': len(production_slips_filtered),
            'production_table_total': len(production_slips),  # 今日作成された全伝票数
            'missing_count': len(missing_slips),
            'extra_count': len(extra_slips)
        }

        cursor.close()
        conn.close()

        # 最新のログから変換処理の詳細を取得
        log_analysis = analyze_recent_conversion_logs()

        # 実際に作成された伝票のサンプルデータを取得
        sample_query = """
        SELECT TOP 5 slip_number, slip_date, store_code, vendor_code, cost_amount, status, created_at
        FROM purchase_slip
        WHERE is_deleted = 0
        AND created_at >= DATEADD(hour, -2, GETDATE())
        ORDER BY created_at DESC
        """
        cursor.execute(sample_query)
        sample_slips = cursor.fetchall()

        sample_data = []
        for slip in sample_slips:
            sample_data.append({
                'slip_number': slip[0],
                'slip_date': str(slip[1]),
                'store_code': slip[2],
                'vendor_code': slip[3],
                'cost_amount': float(slip[4]) if slip[4] else 0,
                'status': slip[5],
                'created_at': str(slip[6])
            })

        return jsonify({
            'success': True,
            'stats': stats,
            'missing_slips': list(missing_slips),
            'missing_details': missing_details,
            'extra_slips': list(extra_slips),
            'log_analysis': log_analysis,
            'sample_data': sample_data
        })

    except Exception as e:
        current_app.logger.error(f"データ比較中にエラー: {str(e)}")
        import traceback
        current_app.logger.error(f"詳細なエラー情報: {traceback.format_exc()}")
        return jsonify({'error': 'データ比較中にエラーが発生しました'}), 500


def analyze_recent_conversion_logs():
    """最新のログファイルから変換処理の詳細を分析"""
    try:
        import os
        from datetime import datetime, timedelta

        log_file_path = 'logs/app.log'
        if not os.path.exists(log_file_path):
            return {'error': 'ログファイルが見つかりません'}

        # 最新の変換処理ログを解析
        failed_slips = []
        successful_slips = []
        error_details = []

        # 今日の日付でフィルタ
        today = datetime.now().strftime('%Y-%m-%d')

        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 最新の変換処理セッションを特定
            conversion_started = False
            for line in lines:
                if today in line:
                    if '=== ワークテーブル変換処理開始 ===' in line:
                        conversion_started = True
                        failed_slips = []
                        successful_slips = []
                        error_details = []
                    elif conversion_started:
                        if '✓ 伝票' in line and '変換完了' in line:
                            # 成功した伝票を抽出
                            parts = line.split('伝票 ')
                            if len(parts) > 1:
                                slip_number = parts[1].split(':')[0].strip()
                                successful_slips.append(slip_number)
                        elif '✗ 伝票' in line and '本番テーブル投入に失敗' in line:
                            # 失敗した伝票を抽出
                            parts = line.split('伝票 ')
                            if len(parts) > 1:
                                slip_number = parts[1].split(' ')[0].strip()
                                failed_slips.append(slip_number)
                        elif 'ERROR' in line and '伝票' in line:
                            # エラー詳細を抽出
                            error_details.append(line.strip())
                        elif '=== 変換処理完了サマリー ===' in line:
                            break

        except Exception as e:
            return {'error': f'ログファイル読み込みエラー: {str(e)}'}

        return {
            'successful_slips': successful_slips,
            'failed_slips': failed_slips,
            'error_details': error_details[-10:],  # 最新の10件のエラー
            'successful_count': len(successful_slips),
            'failed_count': len(failed_slips)
        }

    except Exception as e:
        return {'error': f'ログ分析エラー: {str(e)}'}
