#!/usr/bin/env python3

filename = 'output/Shipment_20250526180742_001837.DAT'

try:
    with open(filename, 'r', encoding='shift_jis') as f:
        lines = f.readlines()
    
    print(f'ファイル: {filename}')
    print(f'総行数: {len(lines)}')
    
    for i, line in enumerate(lines):
        line_content = line.rstrip('\n\r')
        print(f'行{i+1}: 長さ={len(line_content)}, 先頭4文字="{line_content[:4]}"')
        
        # NNN4レコードを見つけたら詳細表示
        if line_content.startswith('NNN4') or (len(line_content) >= 132 and line_content[128:132] == 'NNN4'):
            if line_content.startswith('NNN4'):
                print(f'  前半NNN4: {line_content[:128]}')
            if len(line_content) >= 132 and line_content[128:132] == 'NNN4':
                print(f'  後半NNN4: {line_content[128:256]}')
        
        if i >= 5:  # 最初の6行だけ表示
            break

except Exception as e:
    print(f'エラー: {e}') 