import { loadUserList } from './list.js';

// ユーザー設定画面の初期化
function initializeUserSettings() {
    // 初期表示時にユーザー一覧を読み込む
    loadUserList();

    const userForm = document.getElementById('userForm');
    const registerUserBtn = document.getElementById('registerUserBtn');
    const changePasswordBtn = document.getElementById('changePasswordBtn');

    if (userForm) {
        // フォームのデフォルトの送信を防ぐ
        userForm.addEventListener('submit', (e) => {
            e.preventDefault();
        });

        // パスワード変更ボタンのイベントリスナー
        if (changePasswordBtn) {
            changePasswordBtn.addEventListener('click', async (e) => {
                e.preventDefault();
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirm_password').value;
                
                await handlePasswordChange({
                    username: username,
                    newPassword: password,
                    confirmPassword: confirmPassword
                });
            });
        }

        // 管理者用のユーザー登録ボタンのイベントリスナー
        if (registerUserBtn) {
            registerUserBtn.addEventListener('click', async (e) => {
                e.preventDefault();
                await handleUserRegistration(e);
                loadUserList();  // ユーザー一覧を再読み込み
            });
        }
    }
}

// CSRFトークンの取得
function getCsrfToken() {
    return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
}

// 新規ユーザー登録の処理
async function handleUserRegistration(event) {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;

    if (password.length < 8) {
        showMessage('userFormMessage', 'パスワードは8文字以上で設定してください。', 'danger');
        return;
    }

    if (password !== confirmPassword) {
        showMessage('userFormMessage', 'パスワードが一致しません。', 'danger');
        return;
    }

    try {

        const response = await fetch('/api/users/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': getCsrfToken()
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });

        const result = await response.json();

        if (response.ok) {
            showMessage('userFormMessage', result.message, 'success');
            document.getElementById('userForm').reset();
            // ユーザー一覧を再読み込み
            loadUserList();
        } else {
            showMessage('userFormMessage', result.error, 'danger');
        }
    } catch (error) {
        console.error('Error:', error);
        showMessage('userFormMessage', 'パーザー登録中にエラーが発生しました。', 'danger');
    }
}

// パスワード変更モーダルの初期化
function initializePasswordChangeModal() {
    // モーダル内のパスワード変更
    const savePasswordBtnModal = document.getElementById('savePasswordBtnModal');
    const passwordChangeFormModal = document.getElementById('passwordChangeFormModal');
    
    if (savePasswordBtnModal && passwordChangeFormModal) {
        savePasswordBtnModal.addEventListener('click', () => 
            handlePasswordChange('Modal'));
    }

    // カード内のパスワード変更
    const savePasswordBtnCard = document.getElementById('savePasswordBtnCard');
    const passwordChangeFormCard = document.getElementById('passwordChangeFormCard');
    
    if (savePasswordBtnCard && passwordChangeFormCard) {
        savePasswordBtnCard.addEventListener('click', () => 
            handlePasswordChange('Card'));
    }
}

function validatePassword(password) {
    const errors = [];
    
    if (password.length < 8) {
        errors.push('パスワードは8文字以上である必要があります');
    }
    
    if (!/[A-Z]/.test(password)) {
        errors.push('パスワードには英大文字を含める必要があります');
    }
    
    if (!/[a-z]/.test(password)) {
        errors.push('パスワードには英小文字を含める必要があります');
    }
    
    if (!/[0-9]/.test(password)) {
        errors.push('パスワードには数字を含める必要があります');
    }
    
    return errors;
}

// パスワード変更の処理
async function handlePasswordChange(data) {
    // パスワードの複雑性チェック
    const passwordErrors = validatePassword(data.newPassword);
    if (passwordErrors.length > 0) {
        showMessage('userFormMessage', passwordErrors.join('\n'), 'danger');
        return;
    }

    if (data.newPassword !== data.confirmPassword) {
        showMessage('userFormMessage', 'パスワードが一致しません。', 'danger');
        return;
    }

    try {
        const response = await fetch('/api/users/change-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': getCsrfToken()
            },
            body: JSON.stringify({
                username: data.username,
                password: data.newPassword
            })
        });

        const result = await response.json();

        if (response.ok) {
            showMessage('userFormMessage', result.message, 'success');
            document.getElementById('userForm').reset();
        } else {
            showMessage('userFormMessage', result.error, 'danger');
        }
    } catch (error) {
        console.error('Error:', error);
        showMessage('userFormMessage', 'パスワード変更中にエラーが発生しました。', 'danger');
    }
}

// メッセージ表示用のユーティリティ関数
function showMessage(elementId, message, type = 'success', timeout = 3000) {
    const messageElement = document.getElementById(elementId);
    if (!messageElement) return;

    messageElement.className = `alert alert-${type}`;
    messageElement.textContent = message;
    messageElement.style.display = 'block';

    if (timeout > 0) {
        setTimeout(() => {
            messageElement.style.display = 'none';
        }, timeout);
    }
}

export { initializeUserSettings, showMessage };
