// CSRFトークンの取得
function getCsrfToken() {
    return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
}

// 取引先設定画面の初期化
function initializeVendorSettings() {
    // フォームのセレクタを修正
    const vendorForm = document.getElementById('vendorForm');
    if (vendorForm) {
        vendorForm.addEventListener('submit', handleVendorRegistration);
    }

    // 編集ボタンの処理
    document.querySelectorAll('.edit-btn').forEach(button => {
        button.addEventListener('click', handleEditMode);
    });

    // 保存ボタンの処理
    document.querySelectorAll('.save-btn').forEach(button => {
        button.addEventListener('click', handleVendorUpdate);
    });

    // キャンセルボタンの処理
    document.querySelectorAll('.cancel-btn').forEach(button => {
        button.addEventListener('click', handleCancelEdit);
    });

    // 税区分変更時の消費税率自動設定
    const taxTypeSelect = document.getElementById('tax_type');
    const taxRateInput = document.getElementById('tax_rate');
    if (taxTypeSelect && taxRateInput) {
        taxTypeSelect.addEventListener('change', function() {
            updateTaxRateBasedOnType(this.value, taxRateInput);
        });
    }
}

// 税区分に応じて消費税率を自動設定
function updateTaxRateBasedOnType(taxType, taxRateInput) {
    switch(taxType) {
        case '01': // 原売価内税
            taxRateInput.value = '10.0';
            break;
        case '03': // 非課税
            taxRateInput.value = '0.0';
            break;
        case '05': // 原価外税売価外税
            taxRateInput.value = '8.0';
            break;
        default:
            taxRateInput.value = '10.0';
    }
}

// 新規取引先登録の処理
async function handleVendorRegistration(e) {
    e.preventDefault();
    const submitButton = this.querySelector('button[type="submit"]');
    submitButton.disabled = true;

    try {
        const formData = new FormData(this);
        const vendorData = {
            vendor_code: formData.get('vendor_code'),
            vendor_name: formData.get('vendor_name'),
            tax_type: formData.get('tax_type'),
            tax_rate: formData.get('tax_rate')
        };

        const response = await fetch('/vendor/settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': getCsrfToken()
            },
            body: JSON.stringify(vendorData)
        });

        const result = await response.json();

        if (response.ok) {
            alert(result.message || '取引先を登録しました');
            window.location.reload();
        } else {
            alert(result.error || '取引先の登録に失敗しました');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('通信エラーが発生しました');
    } finally {
        submitButton.disabled = false;
    }
}

// 編集モードの処理
function handleEditMode() {
    const row = this.closest('tr');
    
    // 名前の編集モード
    row.querySelector('.vendor-name').classList.add('d-none');
    row.querySelector('.edit-name').classList.remove('d-none');
    
    // 税区分の編集モード
    row.querySelector('.tax-type-display').classList.add('d-none');
    row.querySelector('.edit-tax-type').classList.remove('d-none');
    
    // 消費税率の編集モード
    row.querySelector('.tax-rate-display').classList.add('d-none');
    row.querySelector('.edit-tax-rate').classList.remove('d-none');
    
    // ボタンの表示切り替え
    this.classList.add('d-none');
    row.querySelector('.save-btn').classList.remove('d-none');
    row.querySelector('.cancel-btn').classList.remove('d-none');

    // 税区分変更時の消費税率自動更新
    const editTaxType = row.querySelector('.edit-tax-type');
    const editTaxRate = row.querySelector('.edit-tax-rate');
    if (editTaxType && editTaxRate) {
        editTaxType.addEventListener('change', function() {
            updateTaxRateBasedOnType(this.value, editTaxRate);
        });
    }
}

// 取引先更新の処理
async function handleVendorUpdate() {
    const row = this.closest('tr');
    const vendorId = this.dataset.vendorId;
    const newName = row.querySelector('.edit-name').value;
    const newTaxType = row.querySelector('.edit-tax-type').value;
    const newTaxRate = row.querySelector('.edit-tax-rate').value;

    // バリデーション
    if (!newName.trim()) {
        alert('取引先名を入力してください');
        return;
    }

    const taxRateValue = parseFloat(newTaxRate);
    if (isNaN(taxRateValue) || taxRateValue < 0 || taxRateValue > 99.9) {
        alert('消費税率は0.0～99.9の範囲で入力してください');
        return;
    }

    try {
        const response = await fetch(`/vendor/settings/${vendorId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': getCsrfToken()
            },
            body: JSON.stringify({
                vendor_name: newName,
                tax_type: newTaxType,
                tax_rate: newTaxRate
            })
        });

        const data = await response.json();
        if (response.ok) {
            window.location.reload();
        } else {
            alert(data.error || '取引先の更新に失敗しました');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('エラーが発生しました');
    }
}

// 編集キャンセルの処理
function handleCancelEdit() {
    const row = this.closest('tr');
    
    // 表示モードに戻す
    row.querySelector('.vendor-name').classList.remove('d-none');
    row.querySelector('.edit-name').classList.add('d-none');
    row.querySelector('.tax-type-display').classList.remove('d-none');
    row.querySelector('.edit-tax-type').classList.add('d-none');
    row.querySelector('.tax-rate-display').classList.remove('d-none');
    row.querySelector('.edit-tax-rate').classList.add('d-none');
    
    // ボタン表示を戻す
    row.querySelector('.edit-btn').classList.remove('d-none');
    row.querySelector('.save-btn').classList.add('d-none');
    this.classList.add('d-none');
    
    // 値を元に戻す
    row.querySelector('.edit-name').value = row.querySelector('.vendor-name').textContent;
    
    // 税区分も元の値に戻す（新しい3つのコード対応）
    const originalTaxTypeText = row.querySelector('.tax-type-display').textContent.trim();
    let originalTaxTypeValue = '01'; // デフォルト値
    if (originalTaxTypeText.includes('01:原売価内税')) {
        originalTaxTypeValue = '01';
    } else if (originalTaxTypeText.includes('03:非課税')) {
        originalTaxTypeValue = '03';
    } else if (originalTaxTypeText.includes('05:原価外税売価外税')) {
        originalTaxTypeValue = '05';
    }
    row.querySelector('.edit-tax-type').value = originalTaxTypeValue;
    
    // 税率も元の値に戻す
    const originalTaxRateText = row.querySelector('.tax-rate-display').textContent.trim();
    const originalTaxRateValue = originalTaxRateText.replace('%', '');
    row.querySelector('.edit-tax-rate').value = originalTaxRateValue;
}

export { initializeVendorSettings };
