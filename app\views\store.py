from flask import Blueprint, render_template, request, jsonify, current_app, flash, redirect, url_for
from flask_login import login_required, current_user
from app import db
from app.models import Store, PurchaseSlip
from datetime import datetime

store = Blueprint('store', __name__, url_prefix='/store')

@store.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    if not current_user.is_approver:
        flash('この画面にアクセスする権限がありません。')
        return redirect(url_for('auth.index'))

    if request.method == 'POST':
        try:
            if request.is_json:
                data = request.get_json()
            else:
                data = request.form

            store_code = data.get('store_code')
            store_name = data.get('store_name')
            store_name_kana = data.get('store_name_kana', '')

            if not store_code or not store_name:
                return jsonify({'error': '必須項目が入力されていません'}), 400

            if not store_code.isdigit() or len(store_code) != 2:
                return jsonify({'error': '店舗コードは2桁の数字で入力してください'}), 400

            if Store.query.filter_by(store_code=store_code).first():
                return jsonify({'error': 'この店舗コードは既に登録されています'}), 400

            # 新しいStoreインスタンスを作成
            store_instance = Store(
                store_code=store_code,
                store_name=store_name,
                store_name_kana=store_name_kana,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            db.session.add(store_instance)
            db.session.commit()
            
            return jsonify({'message': '店舗を登録しました', 'status': 'success'})

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"店舗登録エラー: {str(e)}")
            return jsonify({'error': '店舗の登録に失敗しました'}), 500

    stores = Store.query.order_by(Store.store_code).all()
    return render_template('store_settings.html', stores=stores)

@store.route('/settings/<int:store_id>', methods=['PUT'])
@login_required
def update_store(store_id):
    if not current_user.is_approver:
        return jsonify({'error': '承認権限が必要です'}), 403
    try:
        store_instance = Store.query.get_or_404(store_id)
        data = request.get_json()
        
        # 店舗名の更新
        if 'store_name' in data:
            store_instance.store_name = data['store_name']
        
        # 店舗名カナの更新
        if 'store_name_kana' in data:
            store_instance.store_name_kana = data['store_name_kana']
        
        store_instance.updated_at = datetime.now()
        
        db.session.commit()
        return jsonify({'message': '店舗情報を更新しました'})
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"店舗更新エラー: {str(e)}")
        return jsonify({'error': '店舗の更新に失敗しました'}), 500

@store.route('/<int:store_id>', methods=['DELETE'])
@login_required
def delete_store(store_id):
    if not current_user.is_approver:
        return jsonify({'error': '承認権限が必要です'}), 403
    try:
        store_instance = Store.query.get_or_404(store_id)
        
        # 関連する仕入伝票の確認（日付の新しい順に最大5件）
        related_slips = PurchaseSlip.query\
            .filter_by(store_code=store_instance.store_code)\
            .order_by(PurchaseSlip.slip_date.desc())\
            .limit(5)\
            .all()

        # 全件数の取得
        total_slips = PurchaseSlip.query\
            .filter_by(store_code=store_instance.store_code)\
            .count()
            
        if total_slips > 0:
            slip_details = [{
                'slip_number': slip.slip_number,
                'slip_date': slip.slip_date.strftime('%Y-%m-%d'),
                'status': '承認済' if slip.status == 'approved' else '未承認'
            } for slip in related_slips]
            
            return jsonify({
                'error': '関連する仕入伝票が存在するため削除できません',
                'details': {
                    'related_slips': slip_details,
                    'total_slips': total_slips,
                    'showing_slips': len(related_slips)
                }
            }), 400
            
        db.session.delete(store_instance)
        db.session.commit()
        return jsonify({'message': '店舗を削除しました'})
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"店舗削除エラー: {str(e)}")
        return jsonify({'error': '店舗の削除に失敗しました'}), 500 