import re

def validate_password(password):
    """パスワードの複雑性をチェック"""
    errors = []
    
    if len(password) < 8:
        errors.append('パスワードは8文字以上である必要があります')
    
    if not re.search(r'[A-Z]', password):
        errors.append('パスワードには英大文字を含める必要があります')
    
    if not re.search(r'[a-z]', password):
        errors.append('パスワードには英小文字を含める必要があります')
    
    if not re.search(r'[0-9]', password):
        errors.append('パスワードには数字を含める必要があります')
    
    return errors 