{% extends "base.html" %}

{% block head %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/vendor_settings.css') }}">
{% endblock %}

{% block content %}
<div class="vendor-settings-page">

    <!-- 新規登録フォーム -->
    <div class="card mb-4">
        <div class="card-header">
            <h3 class="card-title">新規取引先登録</h3>
        </div>
        <div class="card-body">
            <form id="vendorForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="row">
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label for="vendor_code" class="form-label">取引先コード</label>
                            <input type="text" class="form-control" id="vendor_code" name="vendor_code" required pattern="[0-9]{4}" maxlength="4">
                            <div class="form-text">4桁の数字で入力してください</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="vendor_name" class="form-label">取引先名</label>
                            <input type="text" class="form-control" id="vendor_name" name="vendor_name" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="tax_type" class="form-label">税区分</label>
                            <select class="form-control" id="tax_type" name="tax_type" required>
                                <option value="01" selected>01:原売価内税</option>
                                <option value="03">03:非課税</option>
                                <option value="05">05:原価外税売価外税</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label for="tax_rate" class="form-label">消費税率(%)</label>
                            <input type="number" class="form-control" id="tax_rate" name="tax_rate" step="0.1" min="0" max="99.9" value="10.0" required>
                            <div class="form-text">整数部2桁、小数部1桁</div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">登録</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 取引先一覧 -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">取引先一覧</h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>取引先コード</th>
                            <th>取引先名</th>
                            <th>税区分</th>
                            <th>消費税率</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for vendor in vendors %}
                        <tr>
                            <td>{{ vendor.vendor_code }}</td>
                            <td>
                                <span class="vendor-name">{{ vendor.vendor_name }}</span>
                                <input type="text" class="form-control edit-name d-none" value="{{ vendor.vendor_name }}">
                            </td>
                            <td>
                                <span class="tax-type-display">{{ vendor.get_tax_type_display() }}</span>
                                <select class="form-control edit-tax-type d-none">
                                    <option value="01" {% if vendor.tax_type_code == '01' %}selected{% endif %}>01:原売価内税</option>
                                    <option value="03" {% if vendor.tax_type_code == '03' %}selected{% endif %}>03:非課税</option>
                                    <option value="05" {% if vendor.tax_type_code == '05' %}selected{% endif %}>05:原価外税売価外税</option>
                                </select>
                            </td>
                            <td>
                                <span class="tax-rate-display">{{ vendor.get_tax_rate_display() }}</span>
                                <input type="number" class="form-control edit-tax-rate d-none" 
                                       value="{{ vendor.tax_rate }}" step="0.1" min="0" max="99.9">
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary edit-btn">
                                    <i class="bi bi-pencil"></i> 編集
                                </button>
                                <button class="btn btn-sm btn-outline-success save-btn d-none" data-vendor-id="{{ vendor.id }}">
                                    <i class="bi bi-check"></i> 保存
                                </button>
                                <button class="btn btn-sm btn-outline-secondary cancel-btn d-none">
                                    <i class="bi bi-x"></i> キャンセル
                                </button>
                                <button class="btn btn-sm btn-outline-danger delete-btn" data-vendor-id="{{ vendor.id }}">
                                    <i class="bi bi-trash"></i> 削除
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script type="module">
    import { initializeVendorSettings } from '/static/js/pages/vendor/settings.js';
    import { initializeVendorList } from '/static/js/pages/vendor/list.js';

    document.addEventListener('DOMContentLoaded', function() {
        initializeVendorSettings();
        initializeVendorList();
    });
</script>
{% endblock %}
