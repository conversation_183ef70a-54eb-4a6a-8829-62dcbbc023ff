// CSRFトークンの取得
function getCsrfToken() {
    return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
}

// 店舗設定画面の初期化
function initializeStoreSettings() {
    const storeForm = document.getElementById('storeForm');
    if (storeForm) {
        storeForm.addEventListener('submit', handleStoreRegistration);
    }

    // 編集ボタンの処理
    document.querySelectorAll('.edit-btn').forEach(button => {
        button.addEventListener('click', handleEditMode);
    });

    // 保存ボタンの処理
    document.querySelectorAll('.save-btn').forEach(button => {
        button.addEventListener('click', handleStoreUpdate);
    });

    // キャンセルボタンの処理
    document.querySelectorAll('.cancel-btn').forEach(button => {
        button.addEventListener('click', handleCancelEdit);
    });
}

// 新規店舗登録の処理
async function handleStoreRegistration(e) {
    e.preventDefault();
    const submitButton = this.querySelector('button[type="submit"]');
    submitButton.disabled = true;

    try {
        const formData = new FormData(this);
        const storeData = {
            store_code: formData.get('store_code'),
            store_name: formData.get('store_name'),
            store_name_kana: formData.get('store_name_kana') || ''
        };

        // バリデーション
        if (!storeData.store_code || !storeData.store_name) {
            alert('店舗コードと店舗名は必須項目です');
            return;
        }

        if (!/^\d{2}$/.test(storeData.store_code)) {
            alert('店舗コードは2桁の数字で入力してください');
            return;
        }

        const response = await fetch('/store/settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': getCsrfToken()
            },
            body: JSON.stringify(storeData)
        });

        const result = await response.json();

        if (response.ok) {
            alert(result.message || '店舗を登録しました');
            window.location.reload();
        } else {
            alert(result.error || '店舗の登録に失敗しました');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('通信エラーが発生しました');
    } finally {
        submitButton.disabled = false;
    }
}

// 編集モードの処理
function handleEditMode() {
    const row = this.closest('tr');
    
    // 店舗名の編集モード
    row.querySelector('.store-name').classList.add('d-none');
    row.querySelector('.edit-name').classList.remove('d-none');
    
    // 店舗名カナの編集モード
    row.querySelector('.store-name-kana').classList.add('d-none');
    row.querySelector('.edit-name-kana').classList.remove('d-none');
    
    // ボタンの表示切り替え
    this.classList.add('d-none');
    row.querySelector('.save-btn').classList.remove('d-none');
    row.querySelector('.cancel-btn').classList.remove('d-none');
}

// 店舗更新の処理
async function handleStoreUpdate() {
    const row = this.closest('tr');
    const storeId = this.dataset.storeId;
    const newName = row.querySelector('.edit-name').value;
    const newNameKana = row.querySelector('.edit-name-kana').value;

    // バリデーション
    if (!newName.trim()) {
        alert('店舗名を入力してください');
        return;
    }

    if (newName.length > 50) {
        alert('店舗名は50文字以内で入力してください');
        return;
    }

    if (newNameKana.length > 50) {
        alert('店舗名カナは50文字以内で入力してください');
        return;
    }

    try {
        const response = await fetch(`/store/settings/${storeId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': getCsrfToken()
            },
            body: JSON.stringify({
                store_name: newName,
                store_name_kana: newNameKana
            })
        });

        const data = await response.json();
        if (response.ok) {
            alert(data.message || '店舗情報を更新しました');
            window.location.reload();
        } else {
            alert(data.error || '店舗の更新に失敗しました');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('エラーが発生しました');
    }
}

// 編集キャンセルの処理
function handleCancelEdit() {
    const row = this.closest('tr');
    
    // 表示モードに戻す
    row.querySelector('.store-name').classList.remove('d-none');
    row.querySelector('.edit-name').classList.add('d-none');
    row.querySelector('.store-name-kana').classList.remove('d-none');
    row.querySelector('.edit-name-kana').classList.add('d-none');
    
    // ボタン表示を戻す
    row.querySelector('.edit-btn').classList.remove('d-none');
    row.querySelector('.save-btn').classList.add('d-none');
    this.classList.add('d-none');
    
    // 値を元に戻す
    row.querySelector('.edit-name').value = row.querySelector('.store-name').textContent;
    row.querySelector('.edit-name-kana').value = row.querySelector('.store-name-kana').textContent;
}

export { initializeStoreSettings }; 