<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <handlers>
      <add name="PythonHandler"
           path="*"
           verb="*"
           modules="FastCgiModule"
           scriptProcessor="C:\Windows\System32\venv\Scripts\python.exe|C:\Windows\System32\venv\Lib\site-packages\wfastcgi.py"
           resourceType="Unspecified"
           requireAccess="Script" />
    </handlers>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="30000000" />
      </requestFiltering>
    </security>
  </system.webServer>
  <appSettings>
    <add key="PYTHONPATH" value="C:\inetpub\OCRWEBUI" />
    <add key="WSGI_HANDLER" value="wsgi.application" />
  </appSettings>
</configuration>
