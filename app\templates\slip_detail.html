{% extends "base.html" %}

{% block head %}
{{ super() }}
<!-- slip_detail.css を追加 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/slip_detail.css') }}">
{% endblock %}

{% block content %}
<div class="mb-3 d-flex justify-content-between align-items-center">
    <a href="{{ url_for('slip_list.list') }}" class="text-decoration-none">
        <i class="bi bi-arrow-left"></i> 一覧へ戻る
    </a>
    <div class="send-datetime text-end">
        {% if slip.send_flg %}
            <span>送信日時：{{ slip.send_datetime }}</span>
        {% endif %}
    </div>
</div>

<div class="card slip-detail-form" 
     data-slip-id="{{ slip.id }}"
     data-status="{{ slip.status }}">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">伝票詳細 #{{ slip.slip_number }}</h5>
            <div>
                {% if current_user.is_approver %}
                    {% if slip.status != 'approved' %}
                        <button type="button" 
                                id="slip-delete-btn" 
                                class="btn btn-danger" 
                                data-slip-id="{{ slip.id }}">
                            削除
                        </button>
                        <button type="button" 
                                id="slip-approve-btn" 
                                class="btn btn-primary approve-button" 
                                data-slip-id="{{ slip.id }}"
                                {% if next_pending_slip %}
                                data-next-slip-id="{{ next_pending_slip.id }}"
                                {% endif %}
                                data-no-confirm="true">
                            承認
                        </button>
                    {% else %}
                        <div class="d-flex flex-column align-items-end">
                            <button type="button"
                                    class="btn btn-warning unapprove-btn" 
                                    data-slip-id="{{ slip.id }}">
                                承認取消
                            </button>
                        </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
    <div class="row mb-3 ms-2">
        <!-- 伝票番号入力欄 -->
        <div class="col-md-2 header-form-group">
            <label class="form-label">伝票番号</label>
            <input type="text" class="form-control" name="slip_number" 
                   value="{{ slip.slip_number or '' }}" 
                   pattern="[0-9]{1,10}" maxlength="10" style="text-align: left;" 
                   {% if slip.status == 'approved' %}readonly{% endif %}
                   data-original-value="{{ slip.slip_number or '' }}"
                   required>
        </div>
        <!-- 会社コード入力欄 -->
        <div class="col-md-1 header-form-group">
            <label class="form-label">会社コード</label>
            <input type="text" class="form-control" name="company_code" 
                   value="{{ slip.company_code or '' }}" 
                   pattern="[0-9]{3}" maxlength="3" style="text-align: left;" 
                   {% if slip.status == 'approved' %}readonly{% endif %}>
        </div>
        <!-- 取引先コード入力欄 -->
        <div class="col-md-1 header-form-group">
            <label class="form-label">取引先コード</label>
            <input type="text" class="form-control" name="vendor_code" 
                   value="{{ slip.vendor_code or '' }}" 
                   data-original-value="{{ slip.vendor_code or '' }}"
                   pattern="[0-9]{4}" maxlength="4" style="text-align: left;" 
                   {% if slip.status == 'approved' %}readonly{% endif %}>
        </div>
        <!-- 取引先名入力欄 -->
        <div class="col-md-2 header-form-group">
            <label class="form-label">取引先名</label>
            <input type="text" class="form-control" name="vendor_name" 
                   value="{{ slip.vendor_name or '' }}" 
                   data-original-value="{{ slip.vendor_name or '' }}"
                   readonly>
        </div>
        <!-- 日付入力欄 -->
        <div class="col-md-2 header-form-group">
            <label class="form-label">日付</label>
            <input type="date" class="form-control" name="slip_date" 
                   value="{{ slip.slip_date.strftime('%Y-%m-%d') if slip.slip_date else '' }}" 
                   {% if slip.status == 'approved' %}readonly{% endif %}>
        </div>
        <!-- ステータス表示欄 -->
        <div class="col-md-2 header-form-group">
            <label class="form-label">ステータス</label>
            <div>
                <span class="badge bg-{{ 'success' if slip.status == 'approved' else 'warning' }} fs-6">
                    {{ '承認済' if slip.status == 'approved' else '未承認' }}
                </span>
            </div>
        </div>
    </div>
    <div class="row mb-3 ms-2">
        <!-- 店舗コード入力欄 -->
        <div class="col-md-1 header-form-group">
            <label class="form-label">店舗コード</label>
            <input type="text" class="form-control" name="store_code" 
                   value="{{ slip.store_code or '' }}" 
                   pattern="[0-9]{3}" maxlength="3" style="text-align: left;" 
                   {% if slip.status == 'approved' %}readonly{% endif %}>
        </div>
        <!-- 店舗名表示欄 -->
        <div class="col-md-3 header-form-group">
            <label class="form-label">店舗名</label>
            <input type="text" class="form-control" name="store_name" value="{{ slip.store_name }}" readonly>
        </div>
        <!-- 課コード入力欄 -->
        <div class="col-md-1 header-form-group">
            <label class="form-label">課コード</label>
            <input type="text" class="form-control" name="department_code" 
                   value="{{ slip.department_code or '' }}" 
                   pattern="[0-9]{3}" maxlength="3" 
                   {% if slip.status == 'approved' %}readonly{% endif %}>
        </div>
        <!-- 税区分入力欄 -->
        <div class="col-md-1 header-form-group">
            <label class="form-label">税区分</label>
            <input type="text" class="form-control" name="tax_division_code" 
                   id="tax_division_code" 
                   value="{{ slip.tax_division_code or '' }}" 
                   pattern="[0-9]{2}" maxlength="2" style="text-align: left;" 
                   readonly
                   title="税区分は取引先コードから自動設定されます">
        </div>
        <!-- 消費税率入力欄 -->
        <div class="col-md-1 header-form-group">
            <label class="form-label">消費税率</label>
            <input type="text" class="form-control" name="tax_rate" 
                   id="tax_rate" 
                   value="{{ '{:.1f}'.format(slip.tax_rate|float) if slip.tax_rate else '' }}%" 
                   style="text-align: left;" 
                   readonly
                   title="消費税率は取引先コードから自動設定されます">
        </div>
        <!-- 金額表示欄 -->
        <div class="col-md-4">
            <div class="d-flex justify-content-end align-items-center h-100">
                <div class="d-flex gap-5">
                    <div class="amount-section text-end">
                        <label class="total-amount-label">原価金額合計</label>
                        <h4 class="mb-0 total-amount">{{ "{:,.0f}".format(slip.cost_amount|float) if slip.cost_amount else '0' }} 円</h4>
                    </div>
                    <div class="amount-section text-end">
                        <label class="total-amount-label">売価金額合計</label>
                        <h4 class="mb-0 total-amount">{{ "{:,.0f}".format(slip.selling_amount|float) if slip.selling_amount else '0' }} 円</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>行番号</th>
                    <th>マスタ</th>
                    <th>商品コード</th>
                    <th>商品名</th>
                    <th>課コード/クラスコード</th>
                    <th>数量</th>
                    <th>(マスタ原単価)</th>
                    <th>原単価</th>
                    <th>原価金額</th>
                    <th>(マスタ売単価)</th>
                    <th>売単価</th>
                    <th>売価金額</th>
                </tr>
            </thead>
            <tbody>
                {% for detail in slip.details|sort(attribute='line_number') %}
                <tr class="slip-detail-row" data-detail-id="{{ detail.id }}">
                    <td>{{ "%03d"|format(detail.line_number) }}</td>
                    <td class="master-mark">
                        {% if detail.item_id %}
                            <span class="text-success">○</span>
                        {% else %}
                            <span class="text-danger">✘</span>
                        {% endif %}
                    </td>
                    <td>
                        <input type="text" class="form-control" name="product_code" 
                               value="{{ detail.product_code or '' }}" 
                               pattern="[0-9]{13}" maxlength="13" 
                               {% if slip.status == 'approved' %}readonly{% endif %}>
                    </td>
                    <td>{{ detail.product_name }}</td>
                    <td class="department-class-code">
                        {% set dept_code = detail.item.department.section_code if detail.item_id and detail.item and detail.item.department else '' %}
                        {% set class_code = detail.section.section_code if detail.section_id and detail.section else '' %}
                        {% if dept_code and class_code %}
                            {{ dept_code }}/{{ class_code }}
                        {% elif dept_code %}
                            {{ dept_code }}/
                        {% elif class_code %}
                            /{{ class_code }}
                        {% endif %}
                    </td>
                    <td>
                        <input type="number" class="form-control text-end" name="quantity" 
                               value="{{ detail.quantity or '' }}" 
                               min="0" max="999999" step="1" 
                               {% if slip.status == 'approved' %}readonly{% endif %}>
                    </td>
                    <td class="text-end">
                        {% if detail.item %}
                            {{ "{:,.2f}".format(detail.item.unit_cost|float) }}
                        {% endif %}
                    </td>
                    <td>
                        <input type="number" class="form-control text-end" name="unit_cost" 
                               value="{{ detail.unit_cost or '' }}" 
                               min="0" max="999999.99" step="0.01" 
                               {% if slip.status == 'approved' %}readonly{% endif %}>
                    </td>
                    <td class="text-end cost-amount">{{ "{:,.0f}".format((detail.quantity * detail.unit_cost)|float) }}</td>
                    <td class="text-end">
                        {% if detail.item %}
                            {{ "{:,.0f}".format(detail.item.unit_price|float) }}
                        {% endif %}
                    </td>
                    <td>
                        <input type="text" 
                               name="unit_price" 
                               class="form-control text-end" 
                               value="{{ detail.unit_price|int }}" 
                               {% if slip.status == 'approved' %}disabled{% endif %}>
                    </td>
                    <td class="text-end selling-amount">{{ "{:,.0f}".format((detail.quantity * detail.unit_price)|float) }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
<div class="card mt-4">
    <div class="card-header">
        <h5 class="card-title mb-0">履歴</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table id="historyTable" class="table">
                <thead>
                    <tr>
                        <th>日時</th>
                        <th>操作</th>
                        <th>ユーザー</th>
                        <th>詳細</th>
                    </tr>
                </thead>
                <tbody>
                    {% for history in slip.history %}
                    <tr>
                        <td>{{ history.created_at.strftime('%Y/%m/%d %H:%M:%S') }}</td>
                        <td>
                            <span class="badge bg-{{ 'success' if history.action == 'approve' else 'warning' if history.action == 'unapprove' else 'info' }}">
                                {{ '承認' if history.action == 'approve' else '承認取消' if history.action == 'unapprove' else '編集' }}
                            </span>
                        </td>
                        <td>{{ history.user.username }}</td>
                        <td>
                            {% if history.action == 'approve' %}
                                <span class="text-success">
                                    承認者: {{ history.details.get('approved_by') }}
                                </span>
                            {% elif history.action == 'unapprove' %}
                                <span class="text-warning">承認取消</span>
                            {% elif history.action == 'edit' %}
                                <span class="text-info">
                                    {% set details = history.details|from_json %}
                                    {% if details.detail_id %}
                                        明細ID: {{ details.detail_id }}<br>
                                    {% endif %}
                                    {{ details.changes }}
                                </span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
<div id="validationMessages" class="alert alert-danger d-none"></div>
<div class="total-amount-area">
    <span id="total-cost-amount">原価合計金額: {{ "{:,.0f}".format(slip.cost_amount|float) if slip.cost_amount else '0' }}</span>
    <span id="total-selling-amount">売価合計金額: {{ "{:,.0f}".format(slip.selling_amount|float) if slip.selling_amount else '0' }}</span>
</div>
{% endblock %}
{% block scripts %}
{{ super() }}
<script type="module">
    import { initializeSlipDetail } from "{{ url_for('static', filename='js/pages/slip/detail.js') }}";

    document.addEventListener('DOMContentLoaded', function() {
        initializeSlipDetail();
    });
</script>
{% endblock %}

