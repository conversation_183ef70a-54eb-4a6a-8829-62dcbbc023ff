/* 共通のレイアウト */
body {
    background-color: var(--custom-bg-color);
}

.container {
    max-width: 1980px;
    margin: 0 auto;
    padding: 20px;
}

/* カード共通スタイル */
.card {
    background-color: var(--custom-card-bg);
    border-color: var(--custom-card-border);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: var(--custom-card-bg);
    border-bottom: 1px solid var(--custom-card-border);
    padding: 10px 15px;
    font-weight: bold;
}

/* テーブル共通スタイル */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--custom-text-color);
}

.table th,
.table td {
    background-color: var(--custom-card-bg);
    color: var(--custom-text-color);
    border-bottom-color: var(--custom-card-border);
    vertical-align: middle;
}

.table thead th {
    background-color: var(--custom-card-bg);
    border-bottom: 2px solid var(--custom-card-border);
}

/* フォーム共通スタイル */
.form-control {
    background-color: var(--custom-input-bg);
    border-color: var(--custom-input-border);
    color: var(--custom-text-color);
    padding: 0.25rem 0.5rem;
    height: calc(1.5em + 0.5rem + 2px);
    font-size: 0.875rem;
    border-radius: 4px;
}

.form-control:focus {
    background-color: var(--custom-input-bg);
    color: var(--custom-text-color);
}

/* 数値入力欄の共通スタイル */
input[type="number"] {
    text-align: right;
}

/* ラベル共通スタイル */
.form-label {
    color: var(--custom-text-color);
    font-size: 0.875rem;
    font-weight: normal;
    margin-bottom: 0.25rem;
}

/* ボタン共通スタイル */
.btn {
    margin-right: 5px;
}

/* アラート共通スタイル */
.alert {
    margin-top: 10px;
    padding: 10px;
    border-radius: 4px;
}

/* モーダル共通スタイル */
.modal-content {
    background-color: var(--custom-card-bg);
    border-color: var(--custom-card-border);
    border-radius: 8px;
}

.modal-header,
.modal-footer {
    border-color: var(--custom-card-border);
}

/* 見出し共通スタイル */
.card-title {
    color: var(--custom-text-color);
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 0;
}

/* サブ見出し用 */
.card-subtitle {
    color: var(--custom-text-color);
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* アプリケーションタイトル用スタイル */
.app-title {
    color: var(--custom-title-color);
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    padding: 0.75rem 0;
    border-bottom: 2px solid var(--custom-card-border);
}

/* テーマ変数 */
[data-bs-theme="light"] {
    --custom-bg-color: #f8f9fa;
    --custom-text-color: #212529;
    --custom-card-bg: #ffffff;
    --custom-card-border: #dee2e6;
    --custom-table-hover: #f8f9fa;
    --custom-input-bg: #ffffff;
    --custom-input-border: #ced4da;
    --custom-btn-default-bg: #e9ecef;
    --custom-btn-default-border: #dee2e6;
    --total-amount-color: #000000;
    --custom-title-color: #212529;
    --total-amount-bg: #f8f9fa;
    --custom-label-color: #212529;
    --error-box-bg: #fff8f8;
    --error-box-border: #ffcdd2;
    --error-item-bg: rgba(255, 205, 210, 0.2);
    --error-text-color: #dc3545;
}

[data-bs-theme="dark"] {
    --custom-bg-color: #212529;
    --custom-text-color: #f8f9fa;
    --custom-card-bg: #2c3034;
    --custom-card-border: #495057;
    --custom-table-hover: #2c3034;
    --custom-input-bg: #2c3034;
    --custom-input-border: #495057;
    --custom-btn-default-bg: #343a40;
    --custom-btn-default-border: #495057;
    --total-amount-color: #ffffff;
    --custom-title-color: #f8f9fa;
    --total-amount-bg: #2c3034;
    --custom-label-color: #f8f9fa;
    --error-box-bg: #2c2424;
    --error-box-border: #842029;
    --error-item-bg: rgba(220, 53, 69, 0.1);
    --error-text-color: #ea868f;
}

/* 共通のテキストボックススタイル */
input[type="text"],
input[type="number"] {
    background-color: var(--custom-input-bg);
    border-color: var(--custom-input-border);
    color: var(--custom-text-color);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

/* ダークモード用のスタイル */
[data-bs-theme="dark"] input[type="text"],
[data-bs-theme="dark"] input[type="number"] {
    background-color: var(--custom-input-bg);
    border-color: var(--custom-input-border);
    color: var(--custom-text-color);
}
